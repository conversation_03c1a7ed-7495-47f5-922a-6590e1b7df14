// src/lib/services/__tests__/tokenEstimationService.test.ts
// Tests básicos para el servicio de estimación de tokens

import { TokenEstimationService } from '../tokenEstimationService';

describe('TokenEstimationService', () => {
  
  describe('estimateForTests', () => {
    it('debería calcular estimación correcta para tests con contextos pequeños', () => {
      const contextos = ['Contenido de prueba corto'];
      const cantidad = 5;
      
      const estimation = TokenEstimationService.estimateForTests(contextos, cantidad);
      
      expect(estimation.contextTokens).toBeGreaterThan(0);
      expect(estimation.generationTokens).toBe(500 * 5); // 500 tokens por test * 5 tests
      expect(estimation.safetyMargin).toBeGreaterThan(0);
      expect(estimation.totalEstimated).toBeGreaterThan(estimation.contextTokens + estimation.generationTokens);
      expect(estimation.isWithinLimits).toBe(true);
    });
    
    it('debería respetar límites máximos para tests', () => {
      // Crear contexto muy grande para forzar límite
      const contextoGrande = 'a'.repeat(200000); // ~50k tokens
      const contextos = [contextoGrande];
      const cantidad = 100;
      
      const estimation = TokenEstimationService.estimateForTests(contextos, cantidad);
      
      expect(estimation.isWithinLimits).toBe(false);
      expect(estimation.totalEstimated).toBe(50000); // Límite máximo
    });
  });
  
  describe('estimateForFlashcards', () => {
    it('debería calcular estimación correcta para flashcards', () => {
      const contextos = ['Contenido para flashcards'];
      const cantidad = 10;
      
      const estimation = TokenEstimationService.estimateForFlashcards(contextos, cantidad);
      
      expect(estimation.generationTokens).toBe(300 * 10); // 300 tokens por flashcard * 10
      expect(estimation.isWithinLimits).toBe(true);
    });
  });
  
  describe('estimateForMindMaps', () => {
    it('debería calcular estimación independiente de cantidad para mapas mentales', () => {
      const contextos = ['Contenido para mapa mental'];
      
      const estimation = TokenEstimationService.estimateForMindMaps(contextos);
      
      expect(estimation.generationTokens).toBe(4000); // Fijo para mapas mentales
      expect(estimation.isWithinLimits).toBe(true);
    });
  });
  
  describe('estimateForSummaries', () => {
    it('debería calcular estimación independiente de cantidad para resúmenes', () => {
      const contextos = ['Contenido para resumen'];
      
      const estimation = TokenEstimationService.estimateForSummaries(contextos);
      
      expect(estimation.generationTokens).toBe(6000); // Fijo para resúmenes
      expect(estimation.isWithinLimits).toBe(true);
    });
  });
  
  describe('estimateForSummaryEditing', () => {
    it('debería usar menos tokens para edición que para generación', () => {
      const contenidoResumen = 'Resumen existente para editar';
      
      const estimation = TokenEstimationService.estimateForSummaryEditing(contenidoResumen);
      
      expect(estimation.generationTokens).toBe(2000); // Menor que generación (6000)
      expect(estimation.isWithinLimits).toBe(true);
    });
  });
  
  describe('estimateForStudyPlanning', () => {
    it('debería devolver estimación fija para planificación', () => {
      const estimation = TokenEstimationService.estimateForStudyPlanning();
      
      expect(estimation.contextTokens).toBe(0);
      expect(estimation.generationTokens).toBe(20000);
      expect(estimation.safetyMargin).toBe(0);
      expect(estimation.totalEstimated).toBe(20000);
      expect(estimation.isWithinLimits).toBe(true);
    });
  });
  
  describe('validateEstimation', () => {
    it('debería validar estimaciones dentro de límites', () => {
      const validEstimation = {
        contextTokens: 1000,
        generationTokens: 2000,
        safetyMargin: 900,
        totalEstimated: 3900,
        isWithinLimits: true,
        maxAllowed: 50000
      };
      
      const result = TokenEstimationService.validateEstimation(validEstimation, 'TEST_GENERATION');
      
      expect(result.isValid).toBe(true);
      expect(result.reason).toBeUndefined();
    });
    
    it('debería rechazar estimaciones que exceden límites', () => {
      const invalidEstimation = {
        contextTokens: 1000,
        generationTokens: 2000,
        safetyMargin: 900,
        totalEstimated: 60000,
        isWithinLimits: false,
        maxAllowed: 50000
      };
      
      const result = TokenEstimationService.validateEstimation(invalidEstimation, 'TEST_GENERATION');
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toContain('excede límite máximo');
      expect(result.adjustedEstimation?.totalEstimated).toBe(50000);
    });
    
    it('debería rechazar estimaciones demasiado pequeñas', () => {
      const tinyEstimation = {
        contextTokens: 10,
        generationTokens: 20,
        safetyMargin: 9,
        totalEstimated: 39,
        isWithinLimits: true,
        maxAllowed: 50000
      };
      
      const result = TokenEstimationService.validateEstimation(tinyEstimation, 'TEST_GENERATION');
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toContain('demasiado pequeña');
      expect(result.adjustedEstimation?.totalEstimated).toBe(100);
    });
  });
  
  describe('logEstimationAccuracy', () => {
    it('debería loggear precisión sin errores', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      TokenEstimationService.logEstimationAccuracy(
        'TEST_GENERATION',
        1000,
        950,
        'test-user'
      );
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('📊 [TOKEN_ESTIMATION] TEST_GENERATION:'),
        expect.objectContaining({
          estimated: 1000,
          actual: 950,
          userId: 'test-user'
        })
      );
      
      consoleSpy.mockRestore();
    });
    
    it('debería alertar sobre estimaciones imprecisas', () => {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      
      // Estimación con >50% de diferencia
      TokenEstimationService.logEstimationAccuracy(
        'TEST_GENERATION',
        1000,
        500, // 100% de sobreestimación
        'test-user'
      );
      
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ [TOKEN_ESTIMATION] Estimación imprecisa')
      );
      
      consoleWarnSpy.mockRestore();
      consoleLogSpy.mockRestore();
    });
  });
});
