/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/pre-register-paid/route";
exports.ids = ["app/api/auth/pre-register-paid/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v26_src_app_api_auth_pre_register_paid_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/pre-register-paid/route.ts */ \"(rsc)/./src/app/api/auth/pre-register-paid/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/pre-register-paid/route\",\n        pathname: \"/api/auth/pre-register-paid\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/pre-register-paid/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\api\\\\auth\\\\pre-register-paid\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v26_src_app_api_auth_pre_register_paid_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/pre-register-paid/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/auth/pre-register-paid/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/plans */ \"(rsc)/./src/config/plans.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/app/api/auth/pre-register-paid/route.ts\n// API para pre-registrar usuarios de planes de pago antes del checkout\n\n\n\n\nasync function POST(request) {\n    console.log('🚀 [PRE-REGISTER-PAID] Endpoint llamado');\n    try {\n        const body = await request.json();\n        const { email, password, customerName, planId } = body;\n        // --- (Validaciones básicas existentes - sin cambios) ---\n        if (!email || !password || !planId || planId === 'free' || password.length < 6) {\n            if (!email || !password || !planId) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Email, contraseña y plan son requeridos'\n                }, {\n                    status: 400\n                });\n            }\n            if (password.length < 6) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'La contraseña debe tener al menos 6 caracteres'\n                }, {\n                    status: 400\n                });\n            }\n            if (planId === 'free') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Este endpoint es solo para planes de pago'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_2__.getPlanConfiguration)(planId);\n        if (!planConfig) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan no válido'\n            }, {\n                status: 400\n            });\n        }\n        // ---------------------------------------------------------\n        // ===== INICIO DE LA NUEVA LÓGICA =====\n        // 1. VERIFICAR SI EL USUARIO YA EXISTE\n        const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.SupabaseAdminService.getUserByEmail(email);\n        let userId;\n        if (existingUser) {\n            // **CASO: USUARIO EXISTENTE (UPGRADE)**\n            console.log(`🔄 [PRE-REGISTER-PAID] Usuario existente detectado: ${existingUser.id}. Iniciando flujo de upgrade.`);\n            userId = existingUser.id;\n            // Obtener el perfil del usuario para verificar su estado actual\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.SupabaseAdminService.getUserProfile(userId);\n            // Opcional: Añadir lógica para evitar que un usuario \"Pro\" compre un plan \"Usuario\"\n            if (profile && profile.subscription_plan === 'pro' && planId === 'usuario') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Ya tienes un plan superior. No puedes hacer un downgrade desde aquí.'\n                }, {\n                    status: 409\n                });\n            }\n            // Actualizar el perfil para reflejar que está pendiente de un nuevo pago\n            if (profile) {\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.SupabaseAdminService.upsertUserProfile({\n                    ...profile,\n                    security_flags: {\n                        ...profile.security_flags,\n                        awaiting_upgrade_payment: true,\n                        upgrade_target_plan: planId,\n                        upgrade_initiated_at: new Date().toISOString()\n                    },\n                    updated_at: new Date().toISOString()\n                });\n                console.log(`✅ [PRE-REGISTER-PAID] Perfil del usuario existente marcado como 'awaiting_upgrade_payment'.`);\n            } else {\n                console.warn(`⚠️ [PRE-REGISTER-PAID] Usuario existe en Auth pero no tiene perfil. Se procederá, pero se creará un perfil nuevo.`);\n            }\n        } else {\n            // **CASO: USUARIO NUEVO (REGISTRO NORMAL)**\n            console.log(`✨ [PRE-REGISTER-PAID] Usuario nuevo. Iniciando flujo de pre-registro.`);\n            const userData = {\n                name: customerName || email.split('@')[0],\n                plan: planId,\n                payment_verified: false,\n                pre_registered: true,\n                pre_registration_date: new Date().toISOString(),\n                awaiting_payment: true\n            };\n            // NUEVO: Crear usuario con cliente normal para envío automático de email\n            console.log('🚀 Creando usuario con cliente normal para envío automático de email...');\n            const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\");\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: userData,\n                    emailRedirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (error) {\n                // El error de \"User already registered\" no debería ocurrir aquí gracias a la verificación previa,\n                // pero se mantiene por si acaso (condiciones de carrera, etc.)\n                if (error.message?.includes('User already registered')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Ya existe una cuenta con este email.'\n                    }, {\n                        status: 409\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error creando la cuenta.'\n                }, {\n                    status: 500\n                });\n            }\n            if (!data?.user) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error creando la cuenta'\n                }, {\n                    status: 500\n                });\n            }\n            userId = data.user.id;\n            console.log('✅ Usuario pre-registrado exitosamente:', userId);\n            console.log('📧 Email de confirmación enviado automáticamente por Supabase');\n            // Crear perfil de usuario en estado pendiente\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.SupabaseAdminService.createUserProfile({\n                user_id: userId,\n                subscription_plan: planId,\n                monthly_token_limit: (0,_config_plans__WEBPACK_IMPORTED_MODULE_2__.getTokenLimitForPlan)(planId),\n                payment_verified: false,\n                plan_features: planConfig.features,\n                security_flags: {\n                    pre_registered: true,\n                    awaiting_payment: true,\n                    created_at: new Date().toISOString()\n                }\n            });\n            console.log('✅ Perfil de usuario creado en estado pendiente');\n        }\n        // ===== FIN DE LA NUEVA LÓGICA =====\n        // El resto del flujo es común para ambos casos: devolver el ID de usuario para el checkout.\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            userId: userId,\n            message: existingUser ? 'Usuario existente. Procediendo al pago para actualizar el plan.' : 'Usuario pre-registrado exitosamente. Procediendo al pago.',\n            data: {\n                email: email,\n                planId: planId,\n                awaitingPayment: true\n            }\n        });\n    } catch (error) {\n        console.error('Error procesando pre-registro:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error procesando la solicitud'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/pre-register-paid/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 500000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 500000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();