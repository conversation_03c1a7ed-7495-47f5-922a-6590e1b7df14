// src/lib/services/email/emailNotificationService.ts
// Servicio principal de notificaciones por email (API pública)

import { EmailNotification, EmailStats, FailureStats, UserNotificationsResult, RetryResult, SecurityAlertData } from './types';
import { EmailTemplates } from './emailTemplates';
import { EmailSender } from './emailSender';
import { EmailLogger } from './emailLogger';
import { EmailAnalytics } from './emailAnalytics';

export class EmailNotificationService {
  
  /**
   * Enviar notificación de cancelación de suscripción
   */
  static async sendSubscriptionCancelledNotification(
    userEmail: string,
    userName: string,
    planName: string,
    gracePeriodEnd: string,
    userId?: string
  ): Promise<boolean> {
    try {
      // Generar contenido del email usando template
      const template = EmailTemplates.generateSubscriptionCancelledEmail(
        userName,
        planName,
        gracePeriodEnd
      );

      // Crear notificación
      const notification: EmailNotification = {
        to: userEmail,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        type: 'subscription_cancelled',
        userId,
        metadata: {
          planName,
          gracePeriodEnd,
          userName,
          daysRemaining: Math.ceil(
            (new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          )
        }
      };

      // Enviar email
      return await EmailSender.sendEmail(notification);

    } catch (error) {
      console.error('Error enviando notificación de cancelación:', error);
      return false;
    }
  }

  /**
   * Enviar recordatorio de que el período de gracia está por terminar
   */
  static async sendGracePeriodEndingNotification(
    userEmail: string,
    userName: string,
    planName: string,
    gracePeriodEnd: string,
    userId?: string
  ): Promise<boolean> {
    try {
      // Generar contenido del email usando template
      const template = EmailTemplates.generateGracePeriodEndingEmail(
        userName,
        planName,
        gracePeriodEnd
      );

      // Crear notificación
      const notification: EmailNotification = {
        to: userEmail,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        type: 'grace_period_ending',
        userId,
        metadata: {
          planName,
          gracePeriodEnd,
          userName,
          hoursRemaining: Math.ceil(
            (new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60)
          )
        }
      };

      // Enviar email
      return await EmailSender.sendEmail(notification);

    } catch (error) {
      console.error('Error enviando recordatorio de período de gracia:', error);
      return false;
    }
  }

  /**
   * Enviar notificación genérica
   */
  static async sendGenericNotification(
    userEmail: string,
    userName: string,
    title: string,
    message: string,
    type: 'plan_expired' | 'payment_failed' | 'welcome' | 'other' = 'other',
    userId?: string,
    ctaText?: string,
    ctaUrl?: string
  ): Promise<boolean> {
    try {
      // Generar contenido del email usando template genérico
      const template = EmailTemplates.generateGenericEmail(
        userName,
        title,
        message,
        ctaText,
        ctaUrl
      );

      // Crear notificación
      const notification: EmailNotification = {
        to: userEmail,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        type,
        userId,
        metadata: {
          userName,
          title,
          message,
          ctaText,
          ctaUrl
        }
      };

      // Enviar email
      return await EmailSender.sendEmail(notification);

    } catch (error) {
      console.error('Error enviando notificación genérica:', error);
      return false;
    }
  }

  /**
   * Obtener historial de notificaciones de un usuario
   */
  static async getUserNotifications(
    userId: string,
    limit: number = 50,
    type?: string
  ): Promise<UserNotificationsResult> {
    return await EmailLogger.getUserNotifications(userId, limit, type);
  }

  /**
   * Obtener estadísticas de notificaciones por tipo
   */
  static async getNotificationStats(
    startDate?: string,
    endDate?: string
  ): Promise<EmailStats> {
    return await EmailAnalytics.getNotificationStats(startDate, endDate);
  }

  /**
   * Obtener estadísticas de fallos y errores
   */
  static async getFailureStats(
    startDate?: string,
    endDate?: string
  ): Promise<FailureStats> {
    return await EmailAnalytics.getFailureStats(startDate, endDate);
  }

  /**
   * Reenviar notificaciones fallidas
   */
  static async retryFailedNotifications(
    maxAge: number = 24,
    limit: number = 10
  ): Promise<RetryResult> {
    return await EmailSender.retryFailedNotifications(maxAge, limit);
  }

  /**
   * Obtener métricas de rendimiento
   */
  static async getPerformanceMetrics(
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalSent: number;
    successRate: number;
    avgResponseTime: number;
    peakHours: Record<string, number>;
    dailyVolume: Record<string, number>;
  }> {
    return await EmailAnalytics.getPerformanceMetrics(startDate, endDate);
  }

  /**
   * Obtener top usuarios por volumen de notificaciones
   */
  static async getTopUsersByVolume(
    limit: number = 10,
    startDate?: string,
    endDate?: string
  ): Promise<Array<{
    userId: string;
    email: string;
    count: number;
    lastNotification: string;
  }>> {
    return await EmailAnalytics.getTopUsersByVolume(limit, startDate, endDate);
  }

  /**
   * Enviar email de prueba
   */
  static async sendTestEmail(
    to: string,
    providerConfig?: any
  ): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    return await EmailSender.sendTestEmail(to, providerConfig);
  }

  /**
   * Validar configuración del proveedor de email
   */
  static async validateEmailProvider(): Promise<{
    isValid: boolean;
    provider: string;
    message: string;
  }> {
    return await EmailSender.validateEmailProvider();
  }

  /**
   * Limpiar notificaciones antiguas
   */
  static async cleanupOldNotifications(
    daysToKeep: number = 90
  ): Promise<{
    deleted: number;
    error?: string;
  }> {
    return await EmailLogger.cleanupOldNotifications(daysToKeep);
  }

  /**
   * Obtener resumen del sistema de notificaciones
   */
  static async getSystemSummary(): Promise<{
    providerStatus: any;
    recentStats: EmailStats;
    failureStats: FailureStats;
    performanceMetrics: any;
  }> {
    try {
      const [providerStatus, recentStats, failureStats, performanceMetrics] = await Promise.all([
        this.validateEmailProvider(),
        this.getNotificationStats(
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Últimos 7 días
          new Date().toISOString()
        ),
        this.getFailureStats(
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          new Date().toISOString()
        ),
        this.getPerformanceMetrics(
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          new Date().toISOString()
        )
      ]);

      return {
        providerStatus,
        recentStats,
        failureStats,
        performanceMetrics
      };

    } catch (error) {
      console.error('Error obteniendo resumen del sistema:', error);
      throw error;
    }
  }

  /**
   * Enviar email de confirmación para upgrade de plan
   * SEGURIDAD: Este email se envía cuando se detecta un pago para un email existente
   */
  static async sendPlanUpgradeConfirmationEmail(
    userEmail: string,
    userName: string,
    newPlanId: string,
    confirmationToken: string
  ): Promise<boolean> {
    try {
      console.log(`📧 [SECURITY] Enviando email de confirmación de upgrade a: ${userEmail}`);

      // Generar contenido del email usando template
      const template = EmailTemplates.generatePlanUpgradeConfirmation(
        userName,
        newPlanId,
        confirmationToken
      );

      // Crear notificación
      const notification: EmailNotification = {
        to: userEmail,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        type: 'plan_upgrade_confirmation',
        metadata: {
          newPlanId,
          confirmationToken,
          userName,
          securityAction: true,
          sentAt: new Date().toISOString()
        }
      };

      // Enviar email
      const result = await EmailSender.sendEmail(notification);

      if (result) {
        console.log(`✅ [SECURITY] Email de confirmación enviado exitosamente a: ${userEmail}`);
      } else {
        console.error(`❌ [SECURITY] Error enviando email de confirmación a: ${userEmail}`);
      }

      return result;

    } catch (error) {
      console.error('❌ [SECURITY] Error enviando email de confirmación de upgrade:', error);
      return false;
    }
  }

  /**
   * Enviar alerta de seguridad al administrador
   */
  static async sendSecurityAlert(alertData: SecurityAlertData): Promise<boolean> {
    try {
      console.log(`🚨 [SECURITY] Enviando alerta de seguridad al administrador`);
      console.log(`🚨 [SECURITY] Discrepancia: ${alertData.payerEmail} → ${alertData.accountOwnerEmail}`);

      // Lista de emails de administradores (consistente con otros endpoints)
      const ADMIN_EMAILS = [
        '<EMAIL>',
        // Agregar más emails de administradores aquí
      ];

      const adminEmail = ADMIN_EMAILS[0]; // Usar el primer administrador

      // Generar contenido del email usando template
      const template = EmailTemplates.generateSecurityAlertEmail(alertData);

      // Crear notificación
      const notification: EmailNotification = {
        to: adminEmail,
        subject: template.subject,
        htmlContent: template.htmlContent,
        textContent: template.textContent,
        type: 'admin_security_alert',
        metadata: {
          alertType: 'payment_email_mismatch',
          payerEmail: alertData.payerEmail,
          accountOwnerEmail: alertData.accountOwnerEmail,
          requestedPlan: alertData.requestedPlan,
          paymentAmount: alertData.paymentAmount,
          stripeSessionId: alertData.stripeSessionId,
          timestamp: alertData.timestamp,
          actionTaken: alertData.actionTaken,
          securityAlert: true,
          sentAt: new Date().toISOString()
        }
      };

      // Enviar email
      const result = await EmailSender.sendEmail(notification);

      if (result) {
        console.log(`✅ [SECURITY] Alerta de seguridad enviada exitosamente al administrador: ${adminEmail}`);
      } else {
        console.error(`❌ [SECURITY] Error enviando alerta de seguridad al administrador: ${adminEmail}`);
      }

      return result;

    } catch (error) {
      console.error('❌ [SECURITY] Error enviando alerta de seguridad:', error);
      return false;
    }
  }
}
