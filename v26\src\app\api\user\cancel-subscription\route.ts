// src/app/api/user/cancel-subscription/route.ts
// Endpoint para cancelar suscripciones de Stripe

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { stripe } from '@/lib/stripe/config';
import Stripe from 'stripe';

export async function POST(request: NextRequest) {
  try {
    // Verificar que Stripe esté configurado
    if (!stripe) {
      return NextResponse.json(
        { error: 'Stripe no está configurado' },
        { status: 500 }
      );
    }

    // Verificar autenticación
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener el perfil del usuario con el subscription_id
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('stripe_subscription_id, subscription_plan')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Perfil de usuario no encontrado' },
        { status: 404 }
      );
    }

    // Verificar que el usuario tenga una suscripción activa
    if (profile.subscription_plan === 'free' || !profile.stripe_subscription_id) {
      return NextResponse.json(
        { error: 'No tienes una suscripción activa para cancelar' },
        { status: 400 }
      );
    }

    // Cancelar la suscripción en Stripe
    // Nota: Esto cancela al final del período actual, no inmediatamente
    const subscription = await stripe.subscriptions.update(
      profile.stripe_subscription_id,
      {
        cancel_at_period_end: true,
      }
    );

    // Calcular la fecha de expiración del plan
    const planExpiresAt = new Date((subscription as any).current_period_end * 1000).toISOString();

    // Actualizar el estado en la base de datos
    const { error: updateError } = await supabaseAdmin
      .from('user_profiles')
      .update({
        auto_renew: false,
        plan_expires_at: planExpiresAt,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating user profile after subscription cancellation:', updateError);
      // No retornamos error aquí porque la cancelación en Stripe ya se hizo
    }

    // Enviar notificación por email (opcional)
    try {
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/notify-signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'subscription_cancelled',
          userEmail: user.email,
          userName: user.user_metadata?.name || 'Usuario',
          subscriptionPlan: profile.subscription_plan,
          periodEnd: new Date((subscription as any).current_period_end * 1000).toLocaleDateString('es-ES'),
        }),
      });
    } catch (emailError) {
      console.error('Error sending cancellation notification email:', emailError);
      // No fallar la operación por un error de email
    }

    return NextResponse.json({
      success: true,
      message: 'Suscripción cancelada exitosamente',
      details: {
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        periodEnd: new Date((subscription as any).current_period_end * 1000).toISOString(),
      },
    });

  } catch (error) {
    console.error('Error canceling subscription:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: `Error de Stripe: ${error.message}` },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
