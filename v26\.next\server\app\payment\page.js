/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/payment/page";
exports.ids = ["app/payment/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(rsc)/./src/app/payment/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'payment',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/payment/page\",\n        pathname: \"/payment\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjI2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MjYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjI2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(rsc)/./src/app/payment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjI2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGF5bWVudCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYyNlxcXFxzcmNcXFxcYXBwXFxcXHBheW1lbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbfb08cc9359\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MjZcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZiZmIwOGNjOTM1OVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/shared/components/ClientLayout */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\");\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones',\n    icons: {\n        icon: [\n            {\n                url: '/favicon.ico',\n                sizes: 'any'\n            },\n            {\n                url: '/logo.png',\n                type: 'image/png'\n            }\n        ],\n        apple: [\n            {\n                url: '/icon-192.png',\n                sizes: '192x192',\n                type: 'image/png'\n            }\n        ]\n    },\n    manifest: '/manifest.json'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/payment/page.tsx":
/*!**********************************!*\
  !*** ./src/app/payment/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\OposI\\\\\\\\v26\\\\\\\\src\\\\\\\\app\\\\\\\\payment\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/payment/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v26\\src\\features\\shared\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(ssr)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjI2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MjYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjI2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payment/page.tsx */ \"(ssr)/./src/app/payment/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjI2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGF5bWVudCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBMkgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wb3NJXFxcXHYyNlxcXFxzcmNcXFxcYXBwXFxcXHBheW1lbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv26%5C%5Csrc%5C%5Capp%5C%5Cpayment%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/payment/page.tsx":
/*!**********************************!*\
  !*** ./src/app/payment/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PaymentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe/plans */ \"(ssr)/./src/lib/stripe/plans.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n// ===== Archivo: src\\app\\payment\\page.tsx =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PaymentContent() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const planId = searchParams.get('plan') || 'free';\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customerName, setCustomerName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const plan = (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_3__.getPlanById)(planId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PaymentContent.useEffect\": ()=>{\n            if (!plan) {\n                router.push('/'); // Redirigir si el plan no es válido\n            }\n        }\n    }[\"PaymentContent.useEffect\"], [\n        plan,\n        router\n    ]);\n    // ===== Archivo: src/app/payment/page.tsx (Lógica de handleSubmit corregida) =====\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        // Validaciones básicas que aplican a todos los planes\n        if (!email.trim()) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Por favor, ingresa tu email');\n            setIsLoading(false);\n            return;\n        }\n        if (!password.trim() || password.length < 6) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('La contraseña debe tener al menos 6 caracteres');\n            setIsLoading(false);\n            return;\n        }\n        if (password !== confirmPassword) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Las contraseñas no coinciden');\n            setIsLoading(false);\n            return;\n        }\n        try {\n            if (planId === 'free') {\n                // --- FLUJO SIMPLE PARA PLAN GRATUITO ---\n                const registerResponse = await fetch('/api/auth/register-free', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName\n                    })\n                });\n                const registerData = await registerResponse.json();\n                if (registerResponse.ok && registerData.success) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(registerData.message || 'Revisa tu email para confirmar tu cuenta.');\n                    router.push(`/thank-you?plan=${planId}&email_sent=true`);\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(registerData.error || 'Error al crear la cuenta gratuita');\n                }\n            } else {\n                // --- FLUJO PARA PLANES DE PAGO (se mantiene igual) ---\n                const preRegisterResponse = await fetch('/api/auth/pre-register-paid', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password,\n                        customerName,\n                        planId\n                    })\n                });\n                const preRegisterData = await preRegisterResponse.json();\n                if (!preRegisterResponse.ok) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(preRegisterData.error || 'Error al pre-registrar la cuenta.');\n                    return;\n                }\n                const stripeResponse = await fetch('/api/stripe/create-checkout-session', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        planId,\n                        email,\n                        customerName,\n                        userId: preRegisterData.userId\n                    })\n                });\n                const stripeData = await stripeResponse.json();\n                if (stripeResponse.ok && stripeData.url) {\n                    window.location.href = stripeData.url;\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(stripeData.error || 'Error al crear la sesión de pago.');\n                }\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error('Error al procesar la solicitud.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!plan) {\n        // Este return se activará si el useEffect redirige, o si el plan es inválido inicialmente\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: \"Cargando detalles del plan o redirigiendo...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 9\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: plan.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-semibold text-blue-600 mt-2\",\n                            children: [\n                                plan.price === 0 ? 'Gratis' : `€${(plan.price / 100).toFixed(2)}`,\n                                (planId === 'pro' || planId === 'usuario') && plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"/mes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 78\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                    children: \"Caracter\\xedsticas del plan:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: plan.features.map((feature, index)=>{\n                                        // Si es un encabezado (Incluye: o No incluye:) - SIN ICONO\n                                        if (feature === 'Incluye:' || feature === 'No incluye:') {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"mt-4 first:mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-gray-900\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 21\n                                            }, this);\n                                        }\n                                        // Lógica mejorada para determinar si un ítem está bajo \"No incluye:\"\n                                        const isNotIncludedItem = (()=>{\n                                            if (!feature.startsWith('• ')) return false;\n                                            // Buscar hacia atrás el encabezado más cercano\n                                            for(let i = index - 1; i >= 0; i--){\n                                                if (plan.features[i] === 'Incluye:') return false;\n                                                if (plan.features[i] === 'No incluye:') return true;\n                                            }\n                                            return false;\n                                        })();\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start ml-2\",\n                                            children: [\n                                                feature.startsWith('• ') ? isNotIncludedItem ? // Icono de Cruz Roja para características no incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-red-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 25\n                                                }, this) : // Icono de Check Verde para características incluidas\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mt-0.5 mr-3 flex-shrink-0 text-green-500\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 25\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: feature.startsWith('• ') ? feature.substring(2) : feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"<EMAIL>\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Contrase\\xf1a *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    id: \"password\",\n                                                    required: true,\n                                                    value: password,\n                                                    onChange: (e)=>setPassword(e.target.value),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"M\\xednimo 6 caracteres\",\n                                                    disabled: isLoading,\n                                                    minLength: 6\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"confirmPassword\",\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Confirmar Contrase\\xf1a *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    id: \"confirmPassword\",\n                                                    required: true,\n                                                    value: confirmPassword,\n                                                    onChange: (e)=>setConfirmPassword(e.target.value),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                    placeholder: \"Repite tu contrase\\xf1a\",\n                                                    disabled: isLoading,\n                                                    minLength: 6\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                planId === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-md p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"\\uD83D\\uDCE7 Te enviaremos un email para que establezcas tu contrase\\xf1a y actives tu cuenta.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"customerName\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Nombre (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"customerName\",\n                                            value: customerName,\n                                            onChange: (e)=>setCustomerName(e.target.value),\n                                            className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Tu nombre\",\n                                            disabled: isLoading\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? 'Procesando...' : planId === 'free' ? 'Solicitar Acceso Gratuito' : 'Proceder al Pago'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\nfunction PaymentPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Cargando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaymentContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\payment\\\\page.tsx\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/payment/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/BackgroundTasksPanel.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/BackgroundTasksPanel.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundTasksPanel = ()=>{\n    const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__.useBackgroundTasks)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCompleted, setShowCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const totalTasks = activeTasks.length + completedTasks.length;\n    if (totalTasks === 0) {\n        return null;\n    }\n    const getTaskIcon = (task)=>{\n        switch(task.status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 16\n                }, undefined);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTaskTypeLabel = (type)=>{\n        switch(type){\n            case 'mapa-mental':\n                return 'Mapa Mental';\n            case 'test':\n                return 'Test';\n            case 'flashcards':\n                return 'Flashcards';\n            default:\n                return 'Tarea';\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('es-ES', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Tareas (\",\n                                        activeTasks.length,\n                                        \" activas)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-96 overflow-y-auto\",\n                    children: [\n                        activeTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                                    children: \"Tareas Activas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: activeTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        task.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-full h-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-500 h-1.5 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${task.progress}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: formatTime(task.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompleted(!showCompleted),\n                                            className: \"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Completadas (\",\n                                                        completedTasks.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearCompletedTasks,\n                                            className: \"text-xs text-gray-500 hover:text-red-600 transition-colors\",\n                                            children: \"Limpiar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined),\n                                showCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: completedTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        task.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-500 truncate\",\n                                                            children: task.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeTask(task.id),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\components\\\\ui\\\\BackgroundTasksPanel.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackgroundTasksPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/BackgroundTasksPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/constants.ts":
/*!*********************************!*\
  !*** ./src/config/constants.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* binding */ AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* binding */ AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_LIMITS: () => (/* binding */ FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* binding */ FREE_PLAN_LIMITS),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* binding */ NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* binding */ PAYMENT_STATES),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* binding */ PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* binding */ PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* binding */ RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* binding */ REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* binding */ RETRY_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* binding */ SECURITY_RISK_SCORES),\n/* harmony export */   SEVERITY_LEVELS: () => (/* binding */ SEVERITY_LEVELS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* binding */ TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* binding */ TIMEOUTS),\n/* harmony export */   TOKEN_LIMITS: () => (/* binding */ TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* binding */ VALIDATION_MESSAGES)\n/* harmony export */ });\n// src/config/constants.ts\n// Constantes centralizadas del sistema\n// ============================================================================\n// CONSTANTES DE APLICACIÓN\n// ============================================================================\n/**\n * URLs y rutas de la aplicación\n */ const APP_URLS = {\n    BASE: \"http://localhost:3000\" || 0,\n    SITE: \"http://localhost:3000\" || 0,\n    UPGRADE_PLAN: '/upgrade-plan',\n    THANK_YOU: '/thank-you',\n    LOGIN: '/login',\n    DASHBOARD: '/app',\n    PROFILE: '/profile',\n    WELCOME: '/welcome'\n};\n/**\n * Rutas públicas (no requieren autenticación)\n */ const PUBLIC_ROUTES = [\n    '/',\n    '/login',\n    '/payment',\n    '/thank-you',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/auth/callback',\n    '/auth/confirmed',\n    '/auth/unauthorized',\n    '/auth/reset-password',\n    '/auth/confirm-reset',\n    '/api/auth/register-free',\n    '/api/auth/pre-register-paid',\n    '/api/stripe/webhook',\n    '/api/stripe/create-checkout-session',\n    '/api/stripe/create-token-checkout',\n    '/api/notify-signup',\n    '/api/user/status',\n    '/api/health',\n    '/api/auth/initiate-password-setup'\n];\n/**\n * Rutas que requieren autenticación básica\n */ const AUTHENTICATED_ROUTES = [\n    '/app',\n    '/dashboard',\n    '/profile',\n    '/welcome',\n    '/upgrade-plan'\n];\n// ============================================================================\n// CONSTANTES DE VALIDACIÓN Y LÍMITES\n// ============================================================================\n/**\n * Límites de archivos y uploads\n */ const FILE_LIMITS = {\n    MAX_SIZE_MB: 5,\n    MAX_SIZE_BYTES: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.pdf'\n    ]\n};\n/**\n * Límites de texto y contenido\n */ const TEXT_LIMITS = {\n    MIN_PASSWORD_LENGTH: 8,\n    MAX_PASSWORD_LENGTH: 128,\n    MAX_DOCUMENT_TITLE_LENGTH: 200,\n    MAX_DESCRIPTION_LENGTH: 500,\n    MIN_CONTENT_LENGTH: 100,\n    MAX_CONTENT_LENGTH: 50000\n};\n/**\n * Límites de tokens y uso\n */ const TOKEN_LIMITS = {\n    DEFAULT_FREE_LIMIT: 50000,\n    WARNING_THRESHOLD_PERCENTAGE: 80,\n    CRITICAL_THRESHOLD_PERCENTAGE: 90,\n    EXCEEDED_THRESHOLD_PERCENTAGE: 100\n};\n/**\n * Límites de rate limiting\n */ const RATE_LIMITS = {\n    DEFAULT_WINDOW_MINUTES: 60,\n    DEFAULT_MAX_REQUESTS: 100,\n    API_REQUESTS_PER_MINUTE: 60,\n    UPLOAD_REQUESTS_PER_HOUR: 10,\n    AUTH_ATTEMPTS_PER_HOUR: 5\n};\n// ============================================================================\n// CONSTANTES DE TIEMPO Y CONFIGURACIÓN\n// ============================================================================\n/**\n * Timeouts y intervalos\n */ const TIMEOUTS = {\n    SESSION_TIMEOUT_MS: 5 * 60 * 1000,\n    API_TIMEOUT_MS: 30 * 1000,\n    UPLOAD_TIMEOUT_MS: 60 * 1000,\n    RETRY_DELAY_MS: 1000,\n    POLLING_INTERVAL_MS: 2000 // 2 segundos\n};\n/**\n * Configuración de reintentos\n */ const RETRY_CONFIG = {\n    MAX_ATTEMPTS: 3,\n    BACKOFF_MULTIPLIER: 2,\n    INITIAL_DELAY_MS: 1000\n};\n/**\n * Configuración de seguridad\n */ const SECURITY_CONFIG = {\n    ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',\n    REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',\n    AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',\n    ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'\n};\n// ============================================================================\n// CONSTANTES DE MENSAJES Y TEXTOS\n// ============================================================================\n/**\n * Mensajes de error comunes\n */ const ERROR_MESSAGES = {\n    UNAUTHORIZED: 'No autorizado',\n    FORBIDDEN: 'Acceso denegado',\n    NOT_FOUND: 'Recurso no encontrado',\n    INTERNAL_ERROR: 'Error interno del servidor',\n    INVALID_DATA: 'Datos inválidos',\n    USER_NOT_FOUND: 'Usuario no encontrado',\n    PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',\n    PAYMENT_REQUIRED: 'Pago requerido',\n    LIMIT_EXCEEDED: 'Límite excedido',\n    FILE_TOO_LARGE: 'El archivo es demasiado grande',\n    INVALID_FILE_TYPE: 'Tipo de archivo no válido',\n    UPLOAD_FAILED: 'Error al subir el archivo',\n    PROCESSING_ERROR: 'Error al procesar la solicitud',\n    NETWORK_ERROR: 'Error de conexión',\n    TIMEOUT_ERROR: 'Tiempo de espera agotado'\n};\n/**\n * Mensajes de éxito\n */ const SUCCESS_MESSAGES = {\n    UPLOAD_SUCCESS: 'Archivo subido correctamente',\n    SAVE_SUCCESS: 'Guardado correctamente',\n    UPDATE_SUCCESS: 'Actualizado correctamente',\n    DELETE_SUCCESS: 'Eliminado correctamente',\n    PAYMENT_SUCCESS: 'Pago procesado correctamente',\n    REGISTRATION_SUCCESS: 'Registro completado',\n    LOGIN_SUCCESS: 'Sesión iniciada',\n    LOGOUT_SUCCESS: 'Sesión cerrada',\n    PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',\n    EMAIL_SENT: 'Email enviado correctamente'\n};\n/**\n * Mensajes de validación\n */ const VALIDATION_MESSAGES = {\n    REQUIRED_FIELD: 'Este campo es obligatorio',\n    INVALID_EMAIL: 'Email no válido',\n    PASSWORD_TOO_SHORT: `La contraseña debe tener al menos ${TEXT_LIMITS.MIN_PASSWORD_LENGTH} caracteres`,\n    PASSWORD_TOO_LONG: `La contraseña no puede tener más de ${TEXT_LIMITS.MAX_PASSWORD_LENGTH} caracteres`,\n    PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',\n    INVALID_FILE_SIZE: `El archivo no puede superar ${FILE_LIMITS.MAX_SIZE_MB}MB`,\n    INVALID_FILE_FORMAT: 'Formato de archivo no válido',\n    TEXT_TOO_SHORT: `El texto debe tener al menos ${TEXT_LIMITS.MIN_CONTENT_LENGTH} caracteres`,\n    TEXT_TOO_LONG: `El texto no puede superar ${TEXT_LIMITS.MAX_CONTENT_LENGTH} caracteres`\n};\n// ============================================================================\n// CONSTANTES DE ESTADO Y TIPOS\n// ============================================================================\n/**\n * Estados de procesamiento\n */ const PROCESSING_STATES = {\n    IDLE: 'idle',\n    LOADING: 'loading',\n    PROCESSING: 'processing',\n    SUCCESS: 'success',\n    ERROR: 'error',\n    CANCELLED: 'cancelled'\n};\n/**\n * Estados de pago\n */ const PAYMENT_STATES = {\n    PENDING: 'pending',\n    PROCESSING: 'processing',\n    COMPLETED: 'completed',\n    FAILED: 'failed',\n    CANCELLED: 'cancelled',\n    REFUNDED: 'refunded'\n};\n/**\n * Tipos de notificación\n */ const NOTIFICATION_TYPES = {\n    INFO: 'info',\n    SUCCESS: 'success',\n    WARNING: 'warning',\n    ERROR: 'error'\n};\n/**\n * Niveles de severidad\n */ const SEVERITY_LEVELS = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n};\n// ============================================================================\n// CONSTANTES DE CONFIGURACIÓN DE ENTORNO\n// ============================================================================\n/**\n * Variables de entorno requeridas\n */ const REQUIRED_ENV_VARS = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'SUPABASE_SERVICE_ROLE_KEY',\n    'STRIPE_SECRET_KEY',\n    'STRIPE_WEBHOOK_SECRET',\n    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'\n];\n/**\n * Configuración de automatización\n */ const AUTOMATION_CONFIG = {\n    INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),\n    DEFAULT_TRIAL_DAYS: 5,\n    DEFAULT_GRACE_PERIOD_DAYS: 3\n};\n// ============================================================================\n// CONSTANTES DE BUSINESS LOGIC\n// ============================================================================\n/**\n * Costos y precios (en centavos)\n */ const PRICING = {\n    ADDITIONAL_TOKENS_PRICE: 1000,\n    ADDITIONAL_TOKENS_AMOUNT: 1000000,\n    FREE_PLAN_PRICE: 0,\n    BASIC_PLAN_PRICE: 999,\n    PRO_PLAN_PRICE: 1999 // €19.99\n};\n/**\n * Límites de planes gratuitos\n */ const FREE_PLAN_LIMITS = {\n    DOCUMENTS: 1,\n    MIND_MAPS_TRIAL: 2,\n    TESTS_TRIAL: 10,\n    FLASHCARDS_TRIAL: 10,\n    TOKENS_TRIAL: 50000,\n    TRIAL_DAYS: 5\n};\n/**\n * Factores de riesgo de seguridad\n */ const SECURITY_RISK_SCORES = {\n    MISSING_USER_AGENT: 30,\n    BOT_USER_AGENT: 20,\n    EXTERNAL_REFERER: 10,\n    SUSPICIOUS_PATTERN: 25,\n    HIGH_FREQUENCY: 40\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/config/features.ts":
/*!********************************!*\
  !*** ./src/config/features.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* binding */ ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* binding */ ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* binding */ ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   FEATURES_CONFIG: () => (/* binding */ FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* binding */ FEATURE_IDS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* binding */ PLAN_RESTRICTED_ROUTES),\n/* harmony export */   actionToFeature: () => (/* binding */ actionToFeature),\n/* harmony export */   activityToFeature: () => (/* binding */ activityToFeature),\n/* harmony export */   featureRequiresPayment: () => (/* binding */ featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* binding */ getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* binding */ getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* binding */ getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* binding */ getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* binding */ getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* binding */ getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* binding */ getFeaturesForPlan),\n/* harmony export */   isValidFeatureId: () => (/* binding */ isValidFeatureId)\n/* harmony export */ });\n// src/config/features.ts\n// Configuración centralizada de características y funcionalidades\n// ============================================================================\n// CONSTANTES DE FEATURES\n// ============================================================================\n/**\n * Identificadores únicos de características del sistema\n */ const FEATURE_IDS = {\n    DOCUMENT_UPLOAD: 'document_upload',\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_TUTOR_CHAT: 'ai_tutor_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_A1_A2: 'summary_a1_a2'\n};\n/**\n * Acciones que pueden realizarse en el sistema\n */ const ACTION_TYPES = {\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_CHAT: 'ai_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_GENERATION: 'summary_generation'\n};\n/**\n * Configuración de todas las características del sistema\n */ const FEATURES_CONFIG = {\n    [FEATURE_IDS.DOCUMENT_UPLOAD]: {\n        id: FEATURE_IDS.DOCUMENT_UPLOAD,\n        name: 'document_upload',\n        displayName: 'Subida de documentos',\n        description: 'Permite subir y procesar documentos PDF para estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 0,\n        icon: 'FiUpload',\n        route: '/app'\n    },\n    [FEATURE_IDS.TEST_GENERATION]: {\n        id: FEATURE_IDS.TEST_GENERATION,\n        name: 'test_generation',\n        displayName: 'Generación de tests',\n        description: 'Genera tests automáticos basados en el contenido de estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 5000,\n        icon: 'FiFileText',\n        route: '/app/tests'\n    },\n    [FEATURE_IDS.FLASHCARD_GENERATION]: {\n        id: FEATURE_IDS.FLASHCARD_GENERATION,\n        name: 'flashcard_generation',\n        displayName: 'Generación de flashcards',\n        description: 'Crea flashcards inteligentes para memorización efectiva',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 3000,\n        icon: 'FiLayers',\n        route: '/app/flashcards'\n    },\n    [FEATURE_IDS.MIND_MAP_GENERATION]: {\n        id: FEATURE_IDS.MIND_MAP_GENERATION,\n        name: 'mind_map_generation',\n        displayName: 'Generación de mapas mentales',\n        description: 'Genera mapas mentales visuales para mejor comprensión',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 4000,\n        icon: 'FiGitBranch',\n        route: '/app/mindmaps'\n    },\n    [FEATURE_IDS.AI_TUTOR_CHAT]: {\n        id: FEATURE_IDS.AI_TUTOR_CHAT,\n        name: 'ai_tutor_chat',\n        displayName: 'Chat con preparador IA',\n        description: 'Interactúa con un preparador de oposiciones inteligente',\n        category: 'premium',\n        minimumPlans: [\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 2000,\n        icon: 'FiMessageSquare',\n        route: '/app/ai-tutor'\n    },\n    [FEATURE_IDS.STUDY_PLANNING]: {\n        id: FEATURE_IDS.STUDY_PLANNING,\n        name: 'study_planning',\n        displayName: 'Planificación de estudios',\n        description: 'Crea planes de estudio personalizados y estructurados',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 20000,\n        icon: 'FiCalendar',\n        route: '/plan-estudios'\n    },\n    [FEATURE_IDS.SUMMARY_A1_A2]: {\n        id: FEATURE_IDS.SUMMARY_A1_A2,\n        name: 'summary_a1_a2',\n        displayName: 'Resúmenes A1 y A2',\n        description: 'Genera resúmenes especializados para oposiciones A1 y A2',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 6000,\n        icon: 'FiBook',\n        route: '/app/summaries'\n    }\n};\n// ============================================================================\n// MAPEOS Y UTILIDADES\n// ============================================================================\n/**\n * Mapeo de acciones a características\n */ const ACTION_TO_FEATURE_MAP = {\n    [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,\n    [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,\n    [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,\n    [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,\n    [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,\n    [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2\n};\n/**\n * Mapeo de actividades de tokens a características\n */ const ACTIVITY_TO_FEATURE_MAP = {\n    'test_generation': FEATURE_IDS.TEST_GENERATION,\n    'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,\n    'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,\n    'ai_tutor_chat': FEATURE_IDS.AI_TUTOR_CHAT,\n    'study_planning': FEATURE_IDS.STUDY_PLANNING,\n    'summary_a1_a2': FEATURE_IDS.SUMMARY_A1_A2,\n    'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD\n};\n/**\n * Configuración de rutas restringidas por plan\n */ const PLAN_RESTRICTED_ROUTES = {\n    '/plan-estudios': [\n        'pro'\n    ],\n    '/app/ai-tutor': [\n        'usuario',\n        'pro'\n    ],\n    '/app/summaries': [\n        'pro'\n    ],\n    '/app/advanced-features': [\n        'pro'\n    ]\n};\n// ============================================================================\n// FUNCIONES UTILITARIAS\n// ============================================================================\n/**\n * Obtiene la configuración de una característica\n */ function getFeatureConfig(featureId) {\n    return FEATURES_CONFIG[featureId];\n}\n/**\n * Obtiene el nombre para mostrar de una característica\n */ function getFeatureDisplayName(featureId) {\n    const config = FEATURES_CONFIG[featureId];\n    return config?.displayName || featureId;\n}\n/**\n * Obtiene todas las características de una categoría\n */ function getFeaturesByCategory(category) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.category === category);\n}\n/**\n * Obtiene las características disponibles para un plan\n */ function getFeaturesForPlan(planId) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.minimumPlans.includes(planId));\n}\n/**\n * Verifica si una característica requiere pago\n */ function featureRequiresPayment(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.requiresPayment || false;\n}\n/**\n * Obtiene los tokens requeridos para una característica\n */ function getFeatureTokensRequired(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.tokensRequired || 0;\n}\n/**\n * Convierte una acción a su característica correspondiente\n */ function actionToFeature(action) {\n    return ACTION_TO_FEATURE_MAP[action];\n}\n/**\n * Convierte una actividad a su característica correspondiente\n */ function activityToFeature(activity) {\n    return ACTIVITY_TO_FEATURE_MAP[activity];\n}\n/**\n * Obtiene todas las características como array\n */ function getAllFeatures() {\n    return Object.values(FEATURES_CONFIG);\n}\n/**\n * Obtiene los IDs de todas las características\n */ function getAllFeatureIds() {\n    return Object.keys(FEATURES_CONFIG);\n}\n/**\n * Verifica si un ID de característica es válido\n */ function isValidFeatureId(featureId) {\n    return featureId in FEATURES_CONFIG;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/features.ts\n");

/***/ }),

/***/ "(ssr)/./src/config/index.ts":
/*!*****************************!*\
  !*** ./src/config/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES),\n/* harmony export */   FEATURES_CONFIG: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURE_IDS),\n/* harmony export */   FILE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FREE_PLAN_LIMITS),\n/* harmony export */   LIMITS: () => (/* binding */ LIMITS),\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PAYMENT_STATES),\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.PLAN_RESTRICTED_ROUTES),\n/* harmony export */   PRICING: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG),\n/* harmony export */   SECURITY: () => (/* binding */ SECURITY),\n/* harmony export */   SECURITY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS),\n/* harmony export */   TIME_CONFIG: () => (/* binding */ TIME_CONFIG),\n/* harmony export */   TOKEN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES),\n/* harmony export */   actionToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.actionToFeature),\n/* harmony export */   activityToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.activityToFeature),\n/* harmony export */   canPerformAction: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.checkUserFeatureAccess),\n/* harmony export */   featureRequiresPayment: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesForPlan),\n/* harmony export */   getPlanConfiguration: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.isUnlimited),\n/* harmony export */   isValidFeatureId: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.isValidFeatureId)\n/* harmony export */ });\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plans */ \"(ssr)/./src/config/plans.ts\");\n/* harmony import */ var _features__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./features */ \"(ssr)/./src/config/features.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/config/constants.ts\");\n/**\n * Configuración centralizada - Re-exports\n * \n * Este archivo centraliza todas las configuraciones del sistema,\n * proporcionando un punto único de importación para cualquier configuración.\n * \n * Uso:\n * import { PLAN_CONFIGURATIONS, FEATURES_CONFIG, ERROR_MESSAGES } from '@/config';\n */ // ============================================================================\n// CONFIGURACIONES DE PLANES\n// ============================================================================\n\n// ============================================================================\n// CONFIGURACIONES DE FEATURES\n// ============================================================================\n\n// ============================================================================\n// CONSTANTES DEL SISTEMA\n// ============================================================================\n\n// ============================================================================\n// RE-EXPORTS COMBINADOS PARA CONVENIENCIA\n// ============================================================================\n// Importar constantes para uso en re-exports combinados\n\n/**\n * Todas las configuraciones de límites en un solo objeto\n */ const LIMITS = {\n    FILE: _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS,\n    TEXT: _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS,\n    TOKEN: _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS,\n    RATE: _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS\n};\n/**\n * Todas las configuraciones de tiempo en un solo objeto\n */ const TIME_CONFIG = {\n    TIMEOUTS: _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS,\n    RETRY: _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG\n};\n/**\n * Todos los mensajes del sistema en un solo objeto\n */ const MESSAGES = {\n    ERROR: _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES,\n    SUCCESS: _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES,\n    VALIDATION: _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES\n};\n/**\n * Todas las configuraciones de seguridad en un solo objeto\n */ const SECURITY = {\n    CONFIG: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG,\n    RISK_SCORES: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 500000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 500000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config/plans.ts\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInactivityTimer */ \"(ssr)/./src/hooks/useInactivityTimer.ts\");\n/* harmony import */ var _features_auth_components_InactivityWarning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../features/auth/components/InactivityWarning */ \"(ssr)/./src/features/auth/components/InactivityWarning.tsx\");\n\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Start true: loading initial auth state\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInactivityWarning, setShowInactivityWarning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [warningTimeRemaining, setWarningTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(60); // 60 segundos de advertencia\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && \"undefined\" !== 'undefined') {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            // src/contexts/AuthContext.tsx (CORREGIDO)\n            const publicPaths = [\n                '/',\n                '/login',\n                '/payment',\n                '/thank-you',\n                '/auth/callback',\n                '/auth/confirmed',\n                '/auth/unauthorized',\n                '/auth/reset-password',\n                '/auth/confirm-reset',\n                '/auth/confirm-invitation' // <-- **AÑADIR ESTA LÍNEA**\n            ];\n            // Si hay sesión y estamos en /login, redirigir a la aplicación\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/app'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la aplicación usando replace para evitar entradas en el historial\n                    router.replace('/app');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_4__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    // Función para manejar el logout por inactividad\n    const handleInactivityLogout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleInactivityLogout]\": async ()=>{\n            // Cerrar sesión directamente sin mostrar advertencia\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleInactivityLogout]\"], [\n        cerrarSesion\n    ]);\n    // Función para extender la sesión\n    const handleExtendSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleExtendSession]\": ()=>{\n            setShowInactivityWarning(false);\n        // El hook useAutoLogout se reiniciará automáticamente con la actividad\n        }\n    }[\"AuthProvider.useCallback[handleExtendSession]\"], []);\n    // Función para cerrar sesión desde la advertencia\n    const handleLogoutFromWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLogoutFromWarning]\": async ()=>{\n            setShowInactivityWarning(false);\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleLogoutFromWarning]\"], [\n        cerrarSesion\n    ]);\n    // Hook para manejar la inactividad (solo si el usuario está autenticado)\n    const { resetTimer } = (0,_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_5__.useAutoLogout)(5, handleInactivityLogout, estaAutenticado() // Solo activo si está autenticado\n    );\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: [\n            children,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ \n\n\nconst BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst BackgroundTasksProvider = ({ children })=>{\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": (taskData)=>{\n            const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTask = {\n                ...taskData,\n                id,\n                status: 'pending',\n                createdAt: new Date()\n            };\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": (prev)=>[\n                        ...prev,\n                        newTask\n                    ]\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Iniciando: ${newTask.title}`, {\n                id: `task_start_${id}`,\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    const updateTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": (id, updates)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": (prev)=>prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": (task)=>{\n                            if (task.id === id) {\n                                const updatedTask = {\n                                    ...task,\n                                    ...updates\n                                };\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.loading(`Procesando: ${task.title}`, {\n                                        id: `task_processing_${id}`\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(`Completado: ${task.title}`, {\n                                        id: `task_completed_${id}`,\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(`Error: ${task.title}`, {\n                                        id: `task_error_${id}`,\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    const removeTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": (id)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": (task)=>task.id !== id\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_start_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_processing_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_completed_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.dismiss(`task_error_${id}`);\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    const getTask = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": (id)=>{\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": (task)=>task.id === id\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    const getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": (type)=>{\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": (task)=>task.type === type\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    const clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": ()=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (task)=>task.status !== 'completed' && task.status !== 'error'\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"])\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    const activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    const completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": (task)=>task.status === 'completed' || task.status === 'error'\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": ()=>({\n                tasks,\n                addTask,\n                updateTask,\n                removeTask,\n                getTask,\n                getTasksByType,\n                clearCompletedTasks,\n                activeTasks,\n                completedTasks\n            })\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\nconst useBackgroundTasks = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BackgroundTasksContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthManager.tsx":
/*!******************************************************!*\
  !*** ./src/features/auth/components/AuthManager.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    if (event === 'SIGNED_OUT') {\n                    // Supabase ya maneja la limpieza de tokens internamente\n                    } else if (event === 'SIGNED_IN') {}\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/InactivityWarning.tsx":
/*!************************************************************!*\
  !*** ./src/features/auth/components/InactivityWarning.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiClock,FiRefreshCw!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n\n\n\nconst InactivityWarning = ({ isVisible, timeRemaining, onExtendSession, onLogout })=>{\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(timeRemaining);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            setCountdown(timeRemaining);\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        timeRemaining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            if (!isVisible || countdown <= 0) return;\n            const interval = setInterval({\n                \"InactivityWarning.useEffect.interval\": ()=>{\n                    setCountdown({\n                        \"InactivityWarning.useEffect.interval\": (prev)=>{\n                            if (prev <= 1) {\n                                onLogout();\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"InactivityWarning.useEffect.interval\"]);\n                }\n            }[\"InactivityWarning.useEffect.interval\"], 1000);\n            return ({\n                \"InactivityWarning.useEffect\": ()=>clearInterval(interval)\n            })[\"InactivityWarning.useEffect\"];\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        isVisible,\n        countdown,\n        onLogout\n    ]);\n    if (!isVisible) return null;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 rounded-full p-3 mr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiAlertTriangle, {\n                                className: \"text-yellow-600 text-xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Sesi\\xf3n por expirar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Tu sesi\\xf3n expirar\\xe1 por inactividad\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                                    className: \"text-gray-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-mono font-bold text-gray-900\",\n                                    children: formatTime(countdown)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mt-2\",\n                            children: \"Tiempo restante antes del cierre autom\\xe1tico\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onExtendSession,\n                            className: \"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Continuar sesi\\xf3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onLogout,\n                            className: \"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n                            children: \"Cerrar sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Por seguridad, tu sesi\\xf3n se cerrar\\xe1 autom\\xe1ticamente tras 10 minutos de inactividad\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InactivityWarning);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/InactivityWarning.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(ssr)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _components_ui_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/BackgroundTasksPanel */ \"(ssr)/./src/components/ui/BackgroundTasksPanel.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n // Importar Toaster\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.BackgroundTasksProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        style: {\n                            background: '#363636',\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZmVhdHVyZXMvc2hhcmVkL2NvbXBvbmVudHMvQ2xpZW50TGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUUwQjtBQUM0QjtBQUNzQjtBQUNoQjtBQUNZO0FBQzlCLENBQUMsbUJBQW1CO0FBTS9DLFNBQVNNLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUNsRSxxQkFDRSw4REFBQ0wscUZBQXVCQTtrQkFDdEIsNEVBQUNELCtEQUFZQTs7OEJBQ1gsOERBQUNFLG9FQUFXQTs7Ozs7OEJBQ1osOERBQUNFLG9EQUFPQTtvQkFDTkcsVUFBUyxZQUFZLHlCQUF5Qjs7b0JBQzlDQyxjQUFjO3dCQUNaLHVDQUF1Qzt3QkFDdkNDLFVBQVU7d0JBQ1ZDLE9BQU87NEJBQ0xDLFlBQVk7NEJBQ1pDLE9BQU87d0JBQ1Q7d0JBQ0FDLFNBQVM7NEJBQ1BKLFVBQVU7NEJBQ1ZDLE9BQU87Z0NBQ0xDLFlBQVk7Z0NBQ1pDLE9BQU87NEJBQ1Q7d0JBQ0Y7d0JBQ0FFLE9BQU87NEJBQ0xMLFVBQVU7NEJBQ1ZDLE9BQU87Z0NBQ0xDLFlBQVk7Z0NBQ1pDLE9BQU87NEJBQ1Q7d0JBQ0Y7b0JBQ0Y7Ozs7Ozs4QkFFRiw4REFBQ1QsMkVBQW9CQTs7Ozs7Z0JBQ3BCRzs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYyNlxcc3JjXFxmZWF0dXJlc1xcc2hhcmVkXFxjb21wb25lbnRzXFxDbGllbnRMYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgQmFja2dyb3VuZFRhc2tzUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL0JhY2tncm91bmRUYXNrc0NvbnRleHQnO1xuaW1wb3J0IEF1dGhNYW5hZ2VyIGZyb20gJy4uLy4uL2F1dGgvY29tcG9uZW50cy9BdXRoTWFuYWdlcic7XG5pbXBvcnQgQmFja2dyb3VuZFRhc2tzUGFuZWwgZnJvbSAnQC9jb21wb25lbnRzL3VpL0JhY2tncm91bmRUYXNrc1BhbmVsJztcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnOyAvLyBJbXBvcnRhciBUb2FzdGVyXG5cbmludGVyZmFjZSBDbGllbnRMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudExheW91dCh7IGNoaWxkcmVuIH06IENsaWVudExheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEJhY2tncm91bmRUYXNrc1Byb3ZpZGVyPlxuICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgPEF1dGhNYW5hZ2VyIC8+XG4gICAgICAgIDxUb2FzdGVyXG4gICAgICAgICAgcG9zaXRpb249XCJ0b3AtcmlnaHRcIiAvLyBQb3NpY2nDs24gZGUgbG9zIHRvYXN0c1xuICAgICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgICAgLy8gT3BjaW9uZXMgcG9yIGRlZmVjdG8gcGFyYSBsb3MgdG9hc3RzXG4gICAgICAgICAgICBkdXJhdGlvbjogNTAwMCwgLy8gRHVyYWNpw7NuIGRlIDUgc2VndW5kb3NcbiAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjMzYzNjM2JywgLy8gRXN0aWxvIG9zY3Vyb1xuICAgICAgICAgICAgICBjb2xvcjogJyNmZmYnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHN1Y2Nlc3M6IHtcbiAgICAgICAgICAgICAgZHVyYXRpb246IDMwMDAsXG4gICAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxMGI5ODEnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgICAgZHVyYXRpb246IDUwMDAsXG4gICAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyNlZjQ0NDQnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICAgPEJhY2tncm91bmRUYXNrc1BhbmVsIC8+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgIDwvQmFja2dyb3VuZFRhc2tzUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJBdXRoUHJvdmlkZXIiLCJCYWNrZ3JvdW5kVGFza3NQcm92aWRlciIsIkF1dGhNYW5hZ2VyIiwiQmFja2dyb3VuZFRhc2tzUGFuZWwiLCJUb2FzdGVyIiwiQ2xpZW50TGF5b3V0IiwiY2hpbGRyZW4iLCJwb3NpdGlvbiIsInRvYXN0T3B0aW9ucyIsImR1cmF0aW9uIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiY29sb3IiLCJzdWNjZXNzIiwiZXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useInactivityTimer.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useInactivityTimer.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout),\n/* harmony export */   useInactivityTimer: () => (/* binding */ useInactivityTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n\n\n// Eventos que consideramos como actividad del usuario (definido fuera del hook para estabilidad)\nconst ACTIVITY_EVENTS = [\n    'mousedown',\n    'mousemove',\n    'keypress',\n    'scroll',\n    'touchstart',\n    'click'\n];\n/**\n * Hook para manejar la desconexión automática por inactividad\n */ const useInactivityTimer = ({ timeout, onTimeout, enabled = true })=>{\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const { tasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    // Verificar si hay tareas de IA activas que deberían pausar el timer\n    const hasActiveAITasks = tasks.some((task)=>task.status === 'pending' || task.status === 'processing');\n    // Función para resetear el timer\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n            if (!enabled) return;\n            // Limpiar el timer anterior si existe\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Actualizar la última actividad\n            lastActivityRef.current = Date.now();\n            // Siempre crear un nuevo timer, independientemente de las tareas de IA\n            // La actividad del usuario siempre reinicia el timer\n            timeoutRef.current = setTimeout({\n                \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n                    // Solo ejecutar logout si no hay tareas de IA activas en el momento del timeout\n                    const currentTasks = tasks.filter({\n                        \"useInactivityTimer.useCallback[resetTimer].currentTasks\": (task)=>task.status === 'pending' || task.status === 'processing'\n                    }[\"useInactivityTimer.useCallback[resetTimer].currentTasks\"]);\n                    if (currentTasks.length === 0) {\n                        onTimeout();\n                    } else {\n                        console.log('🔄 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto');\n                        // Reintentar en 1 minuto si hay tareas activas\n                        setTimeout({\n                            \"useInactivityTimer.useCallback[resetTimer]\": ()=>resetTimer()\n                        }[\"useInactivityTimer.useCallback[resetTimer]\"], 60000);\n                    }\n                }\n            }[\"useInactivityTimer.useCallback[resetTimer]\"], timeout);\n        }\n    }[\"useInactivityTimer.useCallback[resetTimer]\"], [\n        timeout,\n        onTimeout,\n        enabled,\n        tasks\n    ]);\n    // Función para limpiar el timer\n    const clearTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[clearTimer]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useInactivityTimer.useCallback[clearTimer]\"], []);\n    // Función para obtener el tiempo restante\n    const getTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[getTimeRemaining]\": ()=>{\n            if (!enabled || !timeoutRef.current) return 0;\n            const elapsed = Date.now() - lastActivityRef.current;\n            const remaining = Math.max(0, timeout - elapsed);\n            return remaining;\n        }\n    }[\"useInactivityTimer.useCallback[getTimeRemaining]\"], [\n        timeout,\n        enabled\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useInactivityTimer.useEffect\": ()=>{\n            if (!enabled) {\n                clearTimer();\n                return;\n            }\n            // Función que maneja los eventos de actividad\n            const handleActivity = {\n                \"useInactivityTimer.useEffect.handleActivity\": ()=>{\n                    resetTimer();\n                }\n            }[\"useInactivityTimer.useEffect.handleActivity\"];\n            // Agregar event listeners\n            ACTIVITY_EVENTS.forEach({\n                \"useInactivityTimer.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useInactivityTimer.useEffect\"]);\n            // Iniciar el timer\n            resetTimer();\n            // Cleanup\n            return ({\n                \"useInactivityTimer.useEffect\": ()=>{\n                    ACTIVITY_EVENTS.forEach({\n                        \"useInactivityTimer.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useInactivityTimer.useEffect\"]);\n                    clearTimer();\n                }\n            })[\"useInactivityTimer.useEffect\"];\n        }\n    }[\"useInactivityTimer.useEffect\"], [\n        enabled,\n        resetTimer,\n        clearTimer\n    ]);\n    return {\n        resetTimer,\n        clearTimer,\n        getTimeRemaining\n    };\n};\n/**\n * Hook simplificado para desconexión automática\n */ const useAutoLogout = (timeoutMinutes = 5, onLogout, enabled = true)=>{\n    const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos\n    return useInactivityTimer({\n        timeout: timeoutMs,\n        onTimeout: onLogout,\n        enabled\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useInactivityTimer.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config */ \"(ssr)/./src/config/index.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 500.000 tokens.',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens.'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return plan?.planConfig || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: `${\"http://localhost:3000\"}/thank-you`,\n    cancel: `${\"http://localhost:3000\"}/upgrade-plan`,\n    webhook: `${\"http://localhost:3000\"}/api/stripe/webhook`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/stripe/plans.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\r\n * Inicia sesión con email y contraseña\r\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\r\n * Cierra la sesión del usuario actual\r\n */ async function cerrarSesion() {\n    try {\n        console.log('🔓 Iniciando proceso de logout...');\n        // Cerrar sesión con scope 'global' para limpiar tanto local como servidor\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut({\n            scope: 'global'\n        });\n        if (error) {\n            console.error('❌ Error en signOut:', error.message);\n            return {\n                error: error.message\n            };\n        }\n        console.log('✅ SignOut exitoso');\n        // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage\n        if (false) {}\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('❌ Error inesperado en logout:', error);\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\r\n * Obtiene la sesión actual del usuario\r\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\r\n * Obtiene el usuario actual\r\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\r\n * Verifica si el usuario está autenticado\r\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            autoRefreshToken: true,\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUksTUFBTTtZQUNKQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsb0JBQW9CLEtBQVEsOENBQThDO1FBQzVFO0lBQ0Y7QUFFSjtBQUVBLCtDQUErQztBQUN4QyxNQUFNQyxXQUFXVCxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MjZcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLCAgICAgICAvLyBQZXJzaXN0aXIgc2VzacOzbiBlbiBlbCBuYXZlZ2Fkb3JcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSwgICAgIC8vIFJlZnJlc2NhciB0b2tlbiBhdXRvbcOhdGljYW1lbnRlXG4gICAgICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSAgICAvLyBFU0VOQ0lBTDogRGV0ZWN0YXIgeSBwcm9jZXNhciB0b2tlbnMgZGUgVVJMXG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _types_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/database */ \"(ssr)/./src/types/database.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n// NOTA: Para usar el cliente del servidor, importar directamente desde './server'\n// import { createServerSupabaseClient } from '@/lib/supabase/server';\n// Re-exportar todos los tipos desde el archivo centralizado de tipos de base de datos\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL3N1cGFiYXNlQ2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSx5RUFBeUU7QUFDdkI7QUFFbEQsa0ZBQWtGO0FBQ2xGLHNFQUFzRTtBQUV0RSxzRkFBc0Y7QUFDckQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYyNlxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzdXBhYmFzZUNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTb2xvIHJlLWV4cG9ydGFyIGVsIGNsaWVudGUgZGVsIG5hdmVnYWRvciBwYXJhIG1hbnRlbmVyIGNvbXBhdGliaWxpZGFkXHJcbmV4cG9ydCB7IGNyZWF0ZUNsaWVudCwgc3VwYWJhc2UgfSBmcm9tICcuL2NsaWVudCc7XHJcblxyXG4vLyBOT1RBOiBQYXJhIHVzYXIgZWwgY2xpZW50ZSBkZWwgc2Vydmlkb3IsIGltcG9ydGFyIGRpcmVjdGFtZW50ZSBkZXNkZSAnLi9zZXJ2ZXInXHJcbi8vIGltcG9ydCB7IGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc2VydmVyJztcclxuXHJcbi8vIFJlLWV4cG9ydGFyIHRvZG9zIGxvcyB0aXBvcyBkZXNkZSBlbCBhcmNoaXZvIGNlbnRyYWxpemFkbyBkZSB0aXBvcyBkZSBiYXNlIGRlIGRhdG9zXHJcbmV4cG9ydCAqIGZyb20gJ0AvdHlwZXMvZGF0YWJhc2UnO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/database.ts":
/*!*******************************!*\
  !*** ./src/types/database.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/**\n * Tipos relacionados con la base de datos Supabase\n *\n * Este archivo contiene todas las interfaces y tipos que representan\n * las entidades de la base de datos y sus relaciones.\n */ // ============================================================================\n// TIPOS BÁSICOS Y ENUMS\n// ============================================================================\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/database.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/react-icons","vendor-chunks/react-hot-toast","vendor-chunks/@heroicons","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpayment%2Fpage&page=%2Fpayment%2Fpage&appPaths=%2Fpayment%2Fpage&pagePath=private-next-app-dir%2Fpayment%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();