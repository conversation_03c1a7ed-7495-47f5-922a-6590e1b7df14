'use client';

import { useEffect, useState } from 'react';
import { useBackgroundTasks } from '@/contexts/BackgroundTasksContext';
import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';

interface UsePlanEstudiosResultsOptions {
  onResult?: (result: PlanEstudiosEstructurado) => void;
  onError?: (error: string) => void;
}

export const usePlanEstudiosResults = (options: UsePlanEstudiosResultsOptions = {}) => {
  const { tasks } = useBackgroundTasks();
  const [latestResult, setLatestResult] = useState<PlanEstudiosEstructurado | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const planEstudiosTasks = tasks.filter(task => task.type === 'plan-estudios');
    
    // Verificar si hay tareas activas
    const hasActiveTasks = planEstudiosTasks.some(task => 
      task.status === 'pending' || task.status === 'processing'
    );
    setIsLoading(hasActiveTasks);

    // Buscar la tarea completada más reciente
    const completedTasks = planEstudiosTasks
      .filter(task => task.status === 'completed' && task.result)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    if (completedTasks.length > 0) {
      const latestTask = completedTasks[0];
      setLatestResult(latestTask.result);
      
      // Ejecutar callback si es la primera vez que vemos este resultado
      if (options.onResult && latestTask.result !== latestResult) {
        options.onResult(latestTask.result);
      }
    }

    // Buscar tareas con error más recientes
    const errorTasks = planEstudiosTasks
      .filter(task => task.status === 'error')
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    if (errorTasks.length > 0 && options.onError) {
      const latestErrorTask = errorTasks[0];
      options.onError(latestErrorTask.error || 'Error desconocido');
    }

  }, [tasks, options, latestResult]);

  return {
    latestResult,
    isLoading,
    hasResults: !!latestResult
  };
};
