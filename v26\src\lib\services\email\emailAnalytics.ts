// src/lib/services/email/emailAnalytics.ts
// Análisis y estadísticas de notificaciones por email

import { supabaseAdmin } from '@/lib/supabase/admin';
import { EmailStats, FailureStats, ErrorCategory } from './types';

export class EmailAnalytics {
  
  /**
   * Obtener estadísticas de notificaciones por tipo
   */
  static async getNotificationStats(
    startDate?: string,
    endDate?: string
  ): Promise<EmailStats> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*');

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      const byType = (notifications || []).reduce((acc, notif) => {
        acc[notif.type] = (acc[notif.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const byStatus = (notifications || []).reduce((acc, notif) => {
        acc[notif.status] = (acc[notif.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const recentNotifications = (notifications || [])
        .sort((a, b) => new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime())
        .slice(0, 10);

      return {
        byType,
        byStatus,
        total: notifications?.length || 0,
        recentNotifications
      };

    } catch (error) {
      console.error('Error obteniendo estadísticas de notificaciones:', error);
      return {
        byType: {},
        byStatus: {},
        total: 0,
        recentNotifications: []
      };
    }
  }

  /**
   * Obtener estadísticas de fallos y errores
   */
  static async getFailureStats(
    startDate?: string,
    endDate?: string
  ): Promise<FailureStats> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*')
        .eq('status', 'failed');

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: failures, error } = await query;

      if (error) {
        throw error;
      }

      // Obtener total de notificaciones para calcular tasa de fallo
      let totalQuery = supabaseAdmin
        .from('email_notifications')
        .select('*', { count: 'exact', head: true });

      if (startDate) {
        totalQuery = totalQuery.gte('sent_at', startDate);
      }

      if (endDate) {
        totalQuery = totalQuery.lte('sent_at', endDate);
      }

      const { count: totalCount } = await totalQuery;

      // Agrupar errores por tipo
      const errorsByType = (failures || []).reduce((acc, failure) => {
        const errorMessage = failure.metadata?.error_message || 'Unknown error';
        const errorType = this.categorizeError(errorMessage);
        acc[errorType] = (acc[errorType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const totalFailures = failures?.length || 0;
      const failureRate = totalCount && totalCount > 0 ? (totalFailures / totalCount) * 100 : 0;

      return {
        totalFailures,
        failureRate: Math.round(failureRate * 100) / 100, // Redondear a 2 decimales
        errorsByType,
        recentFailures: (failures || [])
          .sort((a, b) => new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime())
          .slice(0, 10)
          .map(failure => ({
            id: failure.id,
            type: failure.type,
            recipient: failure.recipient_email,
            error: failure.metadata?.error_message || 'Unknown error',
            failedAt: failure.metadata?.failed_at || failure.sent_at
          }))
      };

    } catch (error) {
      console.error('Error obteniendo estadísticas de fallos:', error);
      return {
        totalFailures: 0,
        failureRate: 0,
        errorsByType: {},
        recentFailures: []
      };
    }
  }

  /**
   * Categorizar errores para estadísticas
   */
  private static categorizeError(errorMessage: string): ErrorCategory {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return 'Network Error';
    }
    
    if (message.includes('invalid') || message.includes('malformed') || message.includes('email')) {
      return 'Invalid Email';
    }
    
    if (message.includes('rate limit') || message.includes('quota') || message.includes('limit')) {
      return 'Rate Limit';
    }
    
    if (message.includes('auth') || message.includes('key') || message.includes('permission')) {
      return 'Authentication Error';
    }
    
    if (message.includes('bounce') || message.includes('reject')) {
      return 'Email Bounced';
    }
    
    return 'Other Error';
  }

  /**
   * Obtener métricas de rendimiento por período
   */
  static async getPerformanceMetrics(
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalSent: number;
    successRate: number;
    avgResponseTime: number;
    peakHours: Record<string, number>;
    dailyVolume: Record<string, number>;
  }> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('*');

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      const totalSent = notifications?.length || 0;
      const successful = notifications?.filter(n => n.status === 'sent').length || 0;
      const successRate = totalSent > 0 ? (successful / totalSent) * 100 : 0;

      // Agrupar por hora del día para encontrar picos
      const peakHours = (notifications || []).reduce((acc, notif) => {
        const hour = new Date(notif.sent_at).getHours();
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Agrupar por día
      const dailyVolume = (notifications || []).reduce((acc, notif) => {
        const day = notif.sent_at.split('T')[0];
        acc[day] = (acc[day] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalSent,
        successRate: Math.round(successRate * 100) / 100,
        avgResponseTime: 0, // TODO: Implementar si se trackea tiempo de respuesta
        peakHours,
        dailyVolume
      };

    } catch (error) {
      console.error('Error obteniendo métricas de rendimiento:', error);
      return {
        totalSent: 0,
        successRate: 0,
        avgResponseTime: 0,
        peakHours: {},
        dailyVolume: {}
      };
    }
  }

  /**
   * Obtener top usuarios por volumen de notificaciones
   */
  static async getTopUsersByVolume(
    limit: number = 10,
    startDate?: string,
    endDate?: string
  ): Promise<Array<{
    userId: string;
    email: string;
    count: number;
    lastNotification: string;
  }>> {
    try {
      let query = supabaseAdmin
        .from('email_notifications')
        .select('user_id, recipient_email, sent_at')
        .not('user_id', 'is', null);

      if (startDate) {
        query = query.gte('sent_at', startDate);
      }

      if (endDate) {
        query = query.lte('sent_at', endDate);
      }

      const { data: notifications, error } = await query;

      if (error) {
        throw error;
      }

      // Agrupar por usuario
      const userStats = (notifications || []).reduce((acc, notif) => {
        const userId = notif.user_id;
        if (!acc[userId]) {
          acc[userId] = {
            userId,
            email: notif.recipient_email,
            count: 0,
            lastNotification: notif.sent_at
          };
        }
        acc[userId].count++;
        if (new Date(notif.sent_at) > new Date(acc[userId].lastNotification)) {
          acc[userId].lastNotification = notif.sent_at;
        }
        return acc;
      }, {} as Record<string, any>);

      // Convertir a array y ordenar por count
      return Object.values(userStats)
        .sort((a: any, b: any) => b.count - a.count)
        .slice(0, limit);

    } catch (error) {
      console.error('Error obteniendo top usuarios:', error);
      return [];
    }
  }
}
