# OposiAI - Plataforma de Estudio para Oposiciones

OposiAI es una plataforma web avanzada diseñada para ayudar a estudiantes de oposiciones a prepararse de manera eficiente utilizando inteligencia artificial.

## 🚀 Características Principales

- **Generación de Tests**: Crea tests personalizados a partir de documentos PDF
- **Flashcards Inteligentes**: Genera tarjetas de estudio automáticamente
- **Chat con IA**: Tutor virtual para resolver dudas
- **Planificación de Estudio**: Calendario y seguimiento de progreso
- **Mapas Mentales**: Visualización de conceptos clave
- **Gestión de Documentos**: Subida y procesamiento de material de estudio

## 🛠️ Stack Tecnológico

- **Frontend**: Next.js 14 (App Router), React 18, TypeScript
- **Backend**: Next.js API Routes, Supabase
- **Base de Datos**: PostgreSQL (Supabase)
- **Autenticación**: Supa<PERSON> Auth
- **Pagos**: Stripe
- **Estilos**: Tailwind CSS
- **Testing**: Jest, React Testing Library
- **Build Tool**: SWC

## 📁 Estructura del Proyecto

```
v16/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Rutas de autenticación
│   │   ├── dashboard/         # Panel principal
│   │   ├── api/               # API Routes
│   │   └── globals.css        # Estilos globales
│   ├── components/            # Componentes reutilizables
│   │   ├── ui/               # Componentes base (botones, inputs, etc.)
│   │   └── layout/           # Componentes de layout
│   ├── features/             # Funcionalidades específicas
│   │   ├── auth/             # Autenticación
│   │   ├── documents/        # Gestión de documentos
│   │   ├── tests/            # Generación y gestión de tests
│   │   ├── flashcards/       # Sistema de flashcards
│   │   ├── chat/             # Chat con IA
│   │   ├── study-plan/       # Planificación de estudio
│   │   ├── mind-maps/        # Mapas mentales
│   │   └── payments/         # Sistema de pagos
│   ├── lib/                  # Servicios y utilidades centralizadas
│   │   ├── supabase/         # Cliente y servicios de Supabase
│   │   ├── stripe/           # Integración con Stripe
│   │   ├── services/         # Servicios de negocio
│   │   └── utils/            # Utilidades generales
│   ├── types/                # Definiciones de TypeScript
│   ├── contexts/             # Contextos de React
│   ├── hooks/                # Hooks personalizados
│   └── __tests__/            # Tests
│       ├── setup/            # Configuración de tests
│       └── integration/      # Tests de integración
├── public/                   # Archivos estáticos
├── __mocks__/               # Mocks para testing
├── jest.config.js           # Configuración de Jest
├── jest.setup.js            # Setup de Jest
├── next.config.js           # Configuración de Next.js
├── tailwind.config.js       # Configuración de Tailwind
└── tsconfig.json           # Configuración de TypeScript
```

## 🚦 Inicio Rápido

### Prerrequisitos

- Node.js 18+ 
- npm o yarn
- Cuenta de Supabase
- Cuenta de Stripe (para pagos)

### Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd OposI/v16
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar variables de entorno**
   ```bash
   cp .env.example .env.local
   ```
   
   Completar las variables en `.env.local`:
   ```env
   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   
   # Stripe
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_webhook_secret
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   
   # App
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Ejecutar en desarrollo**
   ```bash
   npm run dev
   ```

5. **Abrir en el navegador**
   ```
   http://localhost:3000
   ```

## 🧪 Testing

### Ejecutar tests
```bash
# Todos los tests
npm test

# Tests en modo watch
npm run test:watch

# Tests con coverage
npm run test:coverage

# Tests de integración
npm run test:integration
```

### Estructura de Tests
- **Unit Tests**: En cada directorio de feature (`src/features/*/tests/`)
- **Integration Tests**: En `src/__tests__/integration/`
- **Service Tests**: En `src/lib/__tests__/`
- **Utilities**: En `src/__tests__/setup/`

## 🏗️ Desarrollo

### Scripts Disponibles

```bash
npm run dev          # Desarrollo
npm run build        # Build de producción
npm run start        # Servidor de producción
npm run lint         # Linting
npm run type-check   # Verificación de tipos
npm test             # Tests
```

### Convenciones de Código

- **Componentes**: PascalCase (`UserProfile.tsx`)
- **Hooks**: camelCase con prefijo `use` (`useAuth.ts`)
- **Servicios**: PascalCase con sufijo `Service` (`AuthService.ts`)
- **Tipos**: PascalCase (`UserProfile`, `TestQuestion`)
- **Constantes**: UPPER_SNAKE_CASE (`API_ENDPOINTS`)

### Estructura de Componentes

```typescript
// Ejemplo de componente
interface ComponentProps {
  // Props tipadas
}

export const Component: React.FC<ComponentProps> = ({ prop }) => {
  // Hooks
  // Estado local
  // Efectos
  // Handlers
  
  return (
    // JSX
  );
};
```

## 🔧 Configuración

### Next.js
- App Router habilitado
- SWC para compilación rápida
- Optimización de imágenes
- Configuración de rutas API

### TypeScript
- Strict mode habilitado
- Path mapping configurado (`@/` apunta a `src/`)
- Tipos estrictos para mejor DX

### Tailwind CSS
- Configuración personalizada
- Componentes de UI consistentes
- Responsive design

## 📊 Planes y Características

### Plan Free
- 50,000 tokens mensuales
- Generación básica de tests
- Subida de documentos

### Plan Usuario (€10/mes)
- 1,000,000 tokens mensuales
- Chat con IA tutor
- Generación de flashcards
- Tests avanzados

### Plan Pro (€25/mes)
- 5,000,000 tokens mensuales
- Planificación de estudio
- Mapas mentales
- Funciones premium

## 🔐 Seguridad

- Autenticación con Supabase Auth
- Row Level Security (RLS) en base de datos
- Validación de permisos por plan
- Sanitización de inputs
- Rate limiting en APIs

## 🚀 Despliegue

### Vercel (Recomendado)
1. Conectar repositorio a Vercel
2. Configurar variables de entorno
3. Deploy automático en push a main

### Variables de Entorno de Producción
```env
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 📈 Monitoreo

- Logs de aplicación con Supabase
- Métricas de uso de tokens
- Tracking de errores
- Analytics de conversión

## 🤝 Contribución

Ver [CONTRIBUTING.md](./CONTRIBUTING.md) para guías de contribución.

## 📄 Licencia

Este proyecto es privado y propietario.

## 📞 Soporte

Para soporte técnico o consultas:
- Email: <EMAIL>
- Documentación: [docs.oposiai.com](https://docs.oposiai.com)

---

**Versión**: 16.0.0  
**Última actualización**: Enero 2025
