import { NextResponse, type NextRequest } from 'next/server';
import pdfParse from 'pdf-parse';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { FreeAccountService } from '@/lib/services/freeAccountService';

export async function POST(request: NextRequest) {
  try {
    // 1. Verificar autenticación del usuario
    const supabase = await createServerSupabaseClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (!user || authError) {
      return NextResponse.json(
        { error: 'No autorizado. Debes iniciar sesión para subir documentos.' },
        { status: 401 }
      );
    }

    // 2. Obtener el perfil del usuario para determinar su plan
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('subscription_plan')
      .eq('user_id', user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') { // PGRST116 indica "no rows found"
      console.error('Error fetching user profile for plan check:', profileError);
      return NextResponse.json({ error: 'Error al verificar el plan del usuario.' }, { status: 500 });
    }

    // 3. Verificar límites según el plan del usuario
    let canUserUploadBasedOnPlan = true;
    let uploadReason = "";
    let uploadRemaining: number | undefined = undefined;
    let needsUpgradeForUpload = false;

    if (!profile || profile.subscription_plan === 'free') {
      // Solo aplicar reglas de FreeAccountService para usuarios gratuitos
      const freeAccountCheck = await FreeAccountService.canPerformAction(user.id, 'documents', 1);
      canUserUploadBasedOnPlan = freeAccountCheck.allowed;
      uploadReason = freeAccountCheck.reason || "Límite de cuenta gratuita alcanzado";
      uploadRemaining = freeAccountCheck.remaining;
      needsUpgradeForUpload = !freeAccountCheck.allowed;
    } else {
      // Para usuarios con planes de pago ('usuario', 'pro', etc.)
      // Los planes de pago tienen documentos ilimitados
      canUserUploadBasedOnPlan = true;
    }

    if (!canUserUploadBasedOnPlan) {
      return NextResponse.json(
        {
          error: 'Límite de carga de documentos alcanzado',
          reason: uploadReason,
          remaining: uploadRemaining,
          needsUpgrade: needsUpgradeForUpload
        },
        { status: 403 } // Forbidden
      );
    }

    const contentType = request.headers.get('content-type');
    if (!contentType || !contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { error: "El cuerpo de la petición debe ser 'multipart/form-data'." },
        { status: 415 } // Unsupported Media Type
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File | null;

    // Obtener datos adicionales del formulario
    const titulo = formData.get('titulo') as string || '';
    const categoria = formData.get('categoria') as string || undefined;
    const numeroTemaStr = formData.get('numero_tema') as string;
    const numeroTema = numeroTemaStr ? parseInt(numeroTemaStr) : undefined;

    if (!file) {
      return NextResponse.json(
        { error: 'No se proporcionó ningún archivo.' },
        { status: 400 }
      );
    }

    let extractedText: string;
    let originalFileType: string;
    const fileBuffer = await file.arrayBuffer();

    if (file.type === 'application/pdf') {
      try {
        // Import pdf-lib
        const { PDFDocument } = await import('pdf-lib');

        // Load the PDF with pdf-lib
        const pdfDoc = await PDFDocument.load(fileBuffer);
        const numPages = pdfDoc.getPageCount();

        // Define configurable crop percentages for top and bottom
        // Configuración más agresiva para documentos oficiales/académicos
        const CROP_MARGIN_TOP_PERCENT = 0.12; // 12% desde arriba (encabezados más grandes)
        const CROP_MARGIN_BOTTOM_PERCENT = 0.10; // 10% desde abajo (pies de página)

        for (let i = 0; i < numPages; i++) {
          const page = pdfDoc.getPage(i);
          const { width: originalWidth, height: originalHeight } = page.getSize();

          const cropAmountBottom = originalHeight * CROP_MARGIN_BOTTOM_PERCENT;
          const cropAmountTop = originalHeight * CROP_MARGIN_TOP_PERCENT;

          let newY = cropAmountBottom;
          let newHeight = originalHeight - cropAmountBottom - cropAmountTop;

          // Ensure newHeight is not negative
          if (newHeight < 0) {
            console.warn(`Page ${i + 1}: Calculated newHeight (${newHeight}) is negative. Margins (${CROP_MARGIN_TOP_PERCENT*100}%, ${CROP_MARGIN_BOTTOM_PERCENT*100}%) might be too large for page height ${originalHeight}. Resetting to prevent error, page will not be cropped effectively.`);
            newY = 0; // Reset y to prevent invalid box
            newHeight = originalHeight; // Reset height
          }

          // Set the crop box [x, y, width, height]
          // Origin (0,0) is bottom-left
          page.setCropBox(
            0, // x: typically the left edge of the page
            newY, // y: the new bottom edge after cropping
            originalWidth, // width: typically the full width of the page
            newHeight // height: the new height of the page content area
          );
        }

        // Save the modified PDF to a new buffer
        const modifiedPdfBuffer = await pdfDoc.save();
        console.log('PDF modificado, tamaño del buffer:', modifiedPdfBuffer.length);

        // Convert to Buffer and pass to pdf-parse
        const pdfBuffer = Buffer.from(modifiedPdfBuffer);
        console.log('Buffer creado para pdf-parse, tamaño:', pdfBuffer.length);

        // Use the statically imported pdf-parse library
        try {
          // Pass the buffer to pdf-parse
          const pdf = await pdfParse(pdfBuffer);
          extractedText = pdf.text;
          originalFileType = 'pdf';
          console.log('Texto extraído, longitud:', extractedText.length);
        } catch (pdfParseError) {
          console.error('Error específico de pdf-parse:', pdfParseError);
          throw new Error(`Error al procesar PDF con pdf-parse: ${pdfParseError instanceof Error ? pdfParseError.message : 'Error desconocido'}`);
        }
      } catch (parseError) {
        console.error('Error al procesar PDF:', parseError);
        const errorMessage = parseError instanceof Error ? parseError.message : 'Error desconocido al procesar PDF';
        return NextResponse.json(
          { error: 'Error al procesar el archivo PDF.', details: errorMessage },
          { status: 422 } // Unprocessable Entity
        );
      }
    } else if (file.type === 'text/plain') {
      extractedText = Buffer.from(fileBuffer).toString('utf-8');
      originalFileType = 'txt';
    } else {
      return NextResponse.json(
        { error: `Tipo de archivo no soportado: ${file.type}. Solo se permiten .txt y .pdf.` },
        { status: 415 } // Unsupported Media Type
      );
    }

    if (!extractedText || extractedText.trim() === '') {
      return NextResponse.json(
        { error: 'El contenido extraído del archivo está vacío.' },
        { status: 422 } // Unprocessable Entity
      );
    }

    // Importar guardarDocumentoServer para usar en el servidor
    const { guardarDocumentoServer } = await import('@/lib/supabase/documentosService.server');

    const documentId = await guardarDocumentoServer({
      titulo: titulo || file.name, // Usar el título del formulario o el nombre del archivo como fallback
      contenido: extractedText,
      categoria: categoria, // Usar la categoría del formulario
      numero_tema: numeroTema, // Usar el número de tema del formulario
      tipo_original: originalFileType,
    });

    if (documentId) {
      // 4. Incrementar contador de uso SOLO para usuarios gratuitos
      if (!profile || profile.subscription_plan === 'free') {
        try {
          await FreeAccountService.incrementUsageCount(user.id, 'documents', 1);
        } catch (usageError) {
          console.error('Error incrementando contador de documentos para cuenta gratuita:', usageError);
          // No fallar la operación por esto, pero registrar el error
        }
      }
      // Para planes de pago, no necesitamos incrementar contadores de límites

      return NextResponse.json(
        { message: 'Documento procesado y guardado con éxito.', documentId },
        { status: 201 }
      );
    } else {
      return NextResponse.json(
        { error: 'Error al guardar el documento en la base de datos.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error en el endpoint de subida:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    return NextResponse.json(
      { error: 'Error interno del servidor.', details: errorMessage },
      { status: 500 }
    );
  }
}
