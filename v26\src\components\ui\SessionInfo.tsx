'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface SessionInfoProps {
  className?: string;
}

export default function SessionInfo({ className = '' }: SessionInfoProps) {
  const { estaAutenticado } = useAuth();
  const [timeRemaining, setTimeRemaining] = useState<number>(5 * 60); // 5 minutos en segundos
  const [lastActivity, setLastActivity] = useState<number>(Date.now());

  // Actualizar la última actividad cuando hay interacción del usuario
  useEffect(() => {
    if (!estaAutenticado()) return;

    const updateActivity = () => {
      setLastActivity(Date.now());
      setTimeRemaining(5 * 60); // Resetear a 5 minutos
    };

    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
      'keydown'
    ];

    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, [estaAutenticado]);

  // Contador regresivo
  useEffect(() => {
    if (!estaAutenticado()) return;

    const interval = setInterval(() => {
      const elapsed = Math.floor((Date.now() - lastActivity) / 1000);
      const remaining = Math.max(0, (5 * 60) - elapsed);
      setTimeRemaining(remaining);
    }, 1000);

    return () => clearInterval(interval);
  }, [lastActivity, estaAutenticado]);

  // No mostrar si no está autenticado
  if (!estaAutenticado()) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (): string => {
    if (timeRemaining > 120) return 'text-green-600'; // Más de 2 minutos
    if (timeRemaining > 60) return 'text-yellow-600';  // Más de 1 minuto
    return 'text-red-600'; // Menos de 1 minuto
  };

  return (
    <div className={`flex items-center space-x-2 text-xs ${className}`}>
      <div className="flex items-center space-x-1">
        <div className={`w-2 h-2 rounded-full ${timeRemaining > 60 ? 'bg-green-500' : 'bg-red-500 animate-pulse'}`}></div>
        <span className="text-gray-600">Sesión:</span>
        <span className={`font-mono ${getStatusColor()}`}>
          {formatTime(timeRemaining)}
        </span>
      </div>
      <span className="text-gray-400 text-xs">
        (Logout automático en 5min de inactividad)
      </span>
    </div>
  );
}
