// src/__tests__/integration/authFlow.test.ts
// Tests de integración para flujos de autenticación

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider } from '@/contexts/AuthContext';
import { createClient } from '@/lib/supabase/supabaseClient';
import { TEST_TIMEOUTS, MockFactory } from '../setup/testConfig';

// Mock Supabase client
jest.mock('@/lib/supabase/supabaseClient');
const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>;

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn()
  }),
  usePathname: () => '/test-path'
}));

describe('Authentication Flow Integration', () => {
  let mockSupabaseClient: any;
  let user: any;

  beforeEach(() => {
    jest.clearAllMocks();
    user = userEvent.setup();

    // Setup mock Supabase client
    mockSupabaseClient = {
      auth: {
        getUser: jest.fn(),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn(),
        resetPasswordForEmail: jest.fn(),
        updateUser: jest.fn(),
        onAuthStateChange: jest.fn(() => ({
          data: { subscription: { unsubscribe: jest.fn() } }
        }))
      },
      from: jest.fn(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn()
          }))
        })),
        upsert: jest.fn(),
        update: jest.fn(() => ({
          eq: jest.fn()
        }))
      }))
    };

    mockCreateClient.mockReturnValue(mockSupabaseClient);
  });

  describe('Login Flow', () => {
    it('should handle successful login with email and password', async () => {
      const mockUser = MockFactory.createUserProfile({
        user_id: 'test-user-123',
        subscription_plan: 'free'
      });

      // Mock successful login
      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: {
          user: {
            id: 'test-user-123',
            email: '<EMAIL>'
          },
          session: {
            access_token: 'mock-token',
            refresh_token: 'mock-refresh'
          }
        },
        error: null
      });

      // Mock user profile fetch
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: mockUser,
        error: null
      });

      // Mock component that uses auth
      const TestLoginComponent = () => {
        const { user, loading } = useAuth();
        
        if (loading) return <div>Loading...</div>;
        if (user) return <div>Welcome {user.email}</div>;
        
        return (
          <form onSubmit={async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target as HTMLFormElement);
            await signInWithPassword({
              email: formData.get('email') as string,
              password: formData.get('password') as string
            });
          }}>
            <input name="email" type="email" placeholder="Email" />
            <input name="password" type="password" placeholder="Password" />
            <button type="submit">Login</button>
          </form>
        );
      };

      render(
        <AuthProvider>
          <TestLoginComponent />
        </AuthProvider>
      );

      // Fill login form
      await user.type(screen.getByPlaceholderText('Email'), '<EMAIL>');
      await user.type(screen.getByPlaceholderText('Password'), 'password123');
      
      // Submit form
      await user.click(screen.getByRole('button', { name: 'Login' }));

      // Wait for authentication
      await waitFor(() => {
        expect(screen.getByText('Welcome <EMAIL>')).toBeInTheDocument();
      }, { timeout: TEST_TIMEOUTS.INTEGRATION });

      // Verify Supabase was called correctly
      expect(mockSupabaseClient.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    it('should handle login errors gracefully', async () => {
      // Mock login error
      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid login credentials' }
      });

      const TestLoginComponent = () => {
        const [error, setError] = useState<string | null>(null);
        
        const handleLogin = async (email: string, password: string) => {
          try {
            const { error } = await mockSupabaseClient.auth.signInWithPassword({
              email,
              password
            });
            if (error) setError(error.message);
          } catch (err) {
            setError('Login failed');
          }
        };

        return (
          <div>
            {error && <div role="alert">{error}</div>}
            <button onClick={() => handleLogin('<EMAIL>', 'wrongpass')}>
              Login
            </button>
          </div>
        );
      };

      render(<TestLoginComponent />);
      
      await user.click(screen.getByRole('button', { name: 'Login' }));

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('Invalid login credentials');
      });
    });
  });

  describe('Registration Flow', () => {
    it('should handle successful user registration', async () => {
      // Mock successful registration
      mockSupabaseClient.auth.signUp.mockResolvedValue({
        data: {
          user: {
            id: 'new-user-123',
            email: '<EMAIL>',
            email_confirmed_at: null
          },
          session: null
        },
        error: null
      });

      const TestRegisterComponent = () => {
        const [message, setMessage] = useState<string>('');
        
        const handleRegister = async (email: string, password: string) => {
          const { data, error } = await mockSupabaseClient.auth.signUp({
            email,
            password
          });
          
          if (error) {
            setMessage(`Error: ${error.message}`);
          } else if (data.user && !data.session) {
            setMessage('Check your email for confirmation link');
          }
        };

        return (
          <div>
            <button onClick={() => handleRegister('<EMAIL>', 'password123')}>
              Register
            </button>
            {message && <div data-testid="message">{message}</div>}
          </div>
        );
      };

      render(<TestRegisterComponent />);
      
      await user.click(screen.getByRole('button', { name: 'Register' }));

      await waitFor(() => {
        expect(screen.getByTestId('message')).toHaveTextContent(
          'Check your email for confirmation link'
        );
      });

      expect(mockSupabaseClient.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    it('should handle registration validation errors', async () => {
      // Mock validation error
      mockSupabaseClient.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Password should be at least 6 characters' }
      });

      const TestRegisterComponent = () => {
        const [error, setError] = useState<string>('');
        
        const handleRegister = async () => {
          const { error } = await mockSupabaseClient.auth.signUp({
            email: '<EMAIL>',
            password: '123' // Too short
          });
          
          if (error) setError(error.message);
        };

        return (
          <div>
            <button onClick={handleRegister}>Register</button>
            {error && <div role="alert">{error}</div>}
          </div>
        );
      };

      render(<TestRegisterComponent />);
      
      await user.click(screen.getByRole('button', { name: 'Register' }));

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent(
          'Password should be at least 6 characters'
        );
      });
    });
  });

  describe('Password Reset Flow', () => {
    it('should handle password reset request', async () => {
      // Mock successful password reset request
      mockSupabaseClient.auth.resetPasswordForEmail.mockResolvedValue({
        data: {},
        error: null
      });

      const TestPasswordResetComponent = () => {
        const [message, setMessage] = useState<string>('');
        
        const handlePasswordReset = async (email: string) => {
          const { error } = await mockSupabaseClient.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset-password`
          });
          
          if (error) {
            setMessage(`Error: ${error.message}`);
          } else {
            setMessage('Password reset email sent');
          }
        };

        return (
          <div>
            <button onClick={() => handlePasswordReset('<EMAIL>')}>
              Reset Password
            </button>
            {message && <div data-testid="message">{message}</div>}
          </div>
        );
      };

      render(<TestPasswordResetComponent />);
      
      await user.click(screen.getByRole('button', { name: 'Reset Password' }));

      await waitFor(() => {
        expect(screen.getByTestId('message')).toHaveTextContent(
          'Password reset email sent'
        );
      });

      expect(mockSupabaseClient.auth.resetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        { redirectTo: `${window.location.origin}/auth/reset-password` }
      );
    });

    it('should handle password update after reset', async () => {
      // Mock successful password update
      mockSupabaseClient.auth.updateUser.mockResolvedValue({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          }
        },
        error: null
      });

      const TestPasswordUpdateComponent = () => {
        const [message, setMessage] = useState<string>('');
        
        const handlePasswordUpdate = async (newPassword: string) => {
          const { error } = await mockSupabaseClient.auth.updateUser({
            password: newPassword
          });
          
          if (error) {
            setMessage(`Error: ${error.message}`);
          } else {
            setMessage('Password updated successfully');
          }
        };

        return (
          <div>
            <button onClick={() => handlePasswordUpdate('newpassword123')}>
              Update Password
            </button>
            {message && <div data-testid="message">{message}</div>}
          </div>
        );
      };

      render(<TestPasswordUpdateComponent />);
      
      await user.click(screen.getByRole('button', { name: 'Update Password' }));

      await waitFor(() => {
        expect(screen.getByTestId('message')).toHaveTextContent(
          'Password updated successfully'
        );
      });

      expect(mockSupabaseClient.auth.updateUser).toHaveBeenCalledWith({
        password: 'newpassword123'
      });
    });
  });

  describe('Logout Flow', () => {
    it('should handle successful logout', async () => {
      // Mock successful logout
      mockSupabaseClient.auth.signOut.mockResolvedValue({
        error: null
      });

      const TestLogoutComponent = () => {
        const [isLoggedOut, setIsLoggedOut] = useState(false);
        
        const handleLogout = async () => {
          const { error } = await mockSupabaseClient.auth.signOut();
          if (!error) {
            setIsLoggedOut(true);
          }
        };

        return (
          <div>
            {isLoggedOut ? (
              <div>Logged out successfully</div>
            ) : (
              <button onClick={handleLogout}>Logout</button>
            )}
          </div>
        );
      };

      render(<TestLogoutComponent />);
      
      await user.click(screen.getByRole('button', { name: 'Logout' }));

      await waitFor(() => {
        expect(screen.getByText('Logged out successfully')).toBeInTheDocument();
      });

      expect(mockSupabaseClient.auth.signOut).toHaveBeenCalled();
    });
  });

  describe('Auth State Persistence', () => {
    it('should restore user session on app reload', async () => {
      const mockUser = {
        id: 'persisted-user',
        email: '<EMAIL>'
      };

      // Mock session restoration
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });

      const TestAuthPersistenceComponent = () => {
        const { user, loading } = useAuth();
        
        useEffect(() => {
          // Simulate app reload by calling getUser
          mockSupabaseClient.auth.getUser();
        }, []);

        if (loading) return <div>Loading...</div>;
        if (user) return <div>Welcome back {user.email}</div>;
        return <div>Not logged in</div>;
      };

      render(
        <AuthProvider>
          <TestAuthPersistenceComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('<NAME_EMAIL>')).toBeInTheDocument();
      });

      expect(mockSupabaseClient.auth.getUser).toHaveBeenCalled();
    });
  });
});
