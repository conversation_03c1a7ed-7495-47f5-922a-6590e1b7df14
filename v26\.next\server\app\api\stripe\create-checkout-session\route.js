/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/create-checkout-session/route";
exports.ids = ["app/api/stripe/create-checkout-session/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v26_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/create-checkout-session/route.ts */ \"(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/create-checkout-session/route\",\n        pathname: \"/api/stripe/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\api\\\\stripe\\\\create-checkout-session\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v26_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/create-checkout-session/route.ts":
/*!*************************************************************!*\
  !*** ./src/app/api/stripe/create-checkout-session/route.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe/config */ \"(rsc)/./src/lib/stripe/config.ts\");\n// src/app/api/stripe/create-checkout-session/route.ts\n\n\nasync function POST(request) {\n    try {\n        console.log('Stripe API called');\n        const body = await request.json();\n        console.log('Request body:', body);\n        const { planId, email, customerName, userId, registrationData } = body;\n        // Validaciones básicas\n        if (!planId || !email) {\n            console.log('Missing planId or email');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan ID y email son requeridos'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Validating plan:', planId);\n        if (!(0,_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.isValidPlan)(planId)) {\n            console.log('Invalid plan ID:', planId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan ID no válido'\n            }, {\n                status: 400\n            });\n        }\n        const plan = (0,_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.getPlanById)(planId);\n        console.log('Plan found:', plan);\n        if (!plan) {\n            console.log('Plan not found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan no encontrado'\n            }, {\n                status: 404\n            });\n        }\n        // El plan gratuito no requiere pago\n        if (planId === 'free') {\n            console.log('Free plan does not require payment');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'El plan gratuito no requiere pago'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Creating checkout session for:', {\n            planId,\n            email,\n            customerName\n        });\n        // Usar el precio fijo configurado\n        const priceId = plan.stripePriceId;\n        if (!priceId) {\n            console.log('No price ID configured for plan:', planId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Precio no configurado para este plan'\n            }, {\n                status: 500\n            });\n        }\n        console.log('Using price ID:', priceId);\n        // Determinar el modo de pago basado en el plan\n        // Ahora 'usuario' y 'pro' son recurrentes (suscripciones mensuales)\n        const isRecurring = planId === 'pro' || planId === 'usuario';\n        const mode = isRecurring ? 'subscription' : 'payment';\n        console.log('Payment mode:', mode, 'for plan:', planId);\n        // Crear sesión de checkout\n        const sessionConfig = {\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price: priceId,\n                    quantity: 1\n                }\n            ],\n            mode: mode,\n            customer_email: email,\n            client_reference_id: planId,\n            metadata: {\n                planId: planId,\n                customerEmail: email,\n                customerName: customerName || '',\n                userId: userId || '',\n                registrationData: registrationData ? JSON.stringify(registrationData) : '',\n                createdAt: new Date().toISOString(),\n                source: 'oposiai_website',\n                autoActivate: 'true'\n            },\n            success_url: `${_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.APP_URLS.success}?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,\n            cancel_url: `${_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.APP_URLS.cancel}?plan=${planId}`,\n            automatic_tax: {\n                enabled: true\n            },\n            billing_address_collection: 'required',\n            allow_promotion_codes: true\n        };\n        // Agregar configuración específica para suscripciones (AHORA APLICA A 'usuario' Y 'pro')\n        if (isRecurring) {\n            sessionConfig.subscription_data = {\n                metadata: {\n                    planId: planId,\n                    customerEmail: email,\n                    customerName: customerName || '',\n                    userId: userId || '',\n                    registrationData: registrationData ? JSON.stringify(registrationData) : '',\n                    source: 'oposiai_website',\n                    autoActivate: 'true'\n                }\n            };\n        }\n        if (!_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe) {\n            console.error('Stripe not initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error de configuración de Stripe'\n            }, {\n                status: 500\n            });\n        }\n        const session = await _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe.checkout.sessions.create(sessionConfig);\n        console.log('Checkout session created:', session.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            sessionId: session.id,\n            url: session.url\n        });\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');\n        let errorMessage = 'Error al crear la sesión de pago';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n            console.error('Error message:', errorMessage);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: errorMessage,\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/constants.ts":
/*!*********************************!*\
  !*** ./src/config/constants.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* binding */ AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* binding */ AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_LIMITS: () => (/* binding */ FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* binding */ FREE_PLAN_LIMITS),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* binding */ NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* binding */ PAYMENT_STATES),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* binding */ PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* binding */ PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* binding */ RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* binding */ REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* binding */ RETRY_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* binding */ SECURITY_RISK_SCORES),\n/* harmony export */   SEVERITY_LEVELS: () => (/* binding */ SEVERITY_LEVELS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* binding */ TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* binding */ TIMEOUTS),\n/* harmony export */   TOKEN_LIMITS: () => (/* binding */ TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* binding */ VALIDATION_MESSAGES)\n/* harmony export */ });\n// src/config/constants.ts\n// Constantes centralizadas del sistema\n// ============================================================================\n// CONSTANTES DE APLICACIÓN\n// ============================================================================\n/**\n * URLs y rutas de la aplicación\n */ const APP_URLS = {\n    BASE: \"http://localhost:3000\" || 0,\n    SITE: \"http://localhost:3000\" || 0,\n    UPGRADE_PLAN: '/upgrade-plan',\n    THANK_YOU: '/thank-you',\n    LOGIN: '/login',\n    DASHBOARD: '/app',\n    PROFILE: '/profile',\n    WELCOME: '/welcome'\n};\n/**\n * Rutas públicas (no requieren autenticación)\n */ const PUBLIC_ROUTES = [\n    '/',\n    '/login',\n    '/payment',\n    '/thank-you',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/auth/callback',\n    '/auth/confirmed',\n    '/auth/unauthorized',\n    '/auth/reset-password',\n    '/auth/confirm-reset',\n    '/api/auth/register-free',\n    '/api/auth/pre-register-paid',\n    '/api/stripe/webhook',\n    '/api/stripe/create-checkout-session',\n    '/api/stripe/create-token-checkout',\n    '/api/notify-signup',\n    '/api/user/status',\n    '/api/health',\n    '/api/auth/initiate-password-setup'\n];\n/**\n * Rutas que requieren autenticación básica\n */ const AUTHENTICATED_ROUTES = [\n    '/app',\n    '/dashboard',\n    '/profile',\n    '/welcome',\n    '/upgrade-plan'\n];\n// ============================================================================\n// CONSTANTES DE VALIDACIÓN Y LÍMITES\n// ============================================================================\n/**\n * Límites de archivos y uploads\n */ const FILE_LIMITS = {\n    MAX_SIZE_MB: 5,\n    MAX_SIZE_BYTES: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.pdf'\n    ]\n};\n/**\n * Límites de texto y contenido\n */ const TEXT_LIMITS = {\n    MIN_PASSWORD_LENGTH: 8,\n    MAX_PASSWORD_LENGTH: 128,\n    MAX_DOCUMENT_TITLE_LENGTH: 200,\n    MAX_DESCRIPTION_LENGTH: 500,\n    MIN_CONTENT_LENGTH: 100,\n    MAX_CONTENT_LENGTH: 50000\n};\n/**\n * Límites de tokens y uso\n */ const TOKEN_LIMITS = {\n    DEFAULT_FREE_LIMIT: 50000,\n    WARNING_THRESHOLD_PERCENTAGE: 80,\n    CRITICAL_THRESHOLD_PERCENTAGE: 90,\n    EXCEEDED_THRESHOLD_PERCENTAGE: 100\n};\n/**\n * Límites de rate limiting\n */ const RATE_LIMITS = {\n    DEFAULT_WINDOW_MINUTES: 60,\n    DEFAULT_MAX_REQUESTS: 100,\n    API_REQUESTS_PER_MINUTE: 60,\n    UPLOAD_REQUESTS_PER_HOUR: 10,\n    AUTH_ATTEMPTS_PER_HOUR: 5\n};\n// ============================================================================\n// CONSTANTES DE TIEMPO Y CONFIGURACIÓN\n// ============================================================================\n/**\n * Timeouts y intervalos\n */ const TIMEOUTS = {\n    SESSION_TIMEOUT_MS: 5 * 60 * 1000,\n    API_TIMEOUT_MS: 30 * 1000,\n    UPLOAD_TIMEOUT_MS: 60 * 1000,\n    RETRY_DELAY_MS: 1000,\n    POLLING_INTERVAL_MS: 2000 // 2 segundos\n};\n/**\n * Configuración de reintentos\n */ const RETRY_CONFIG = {\n    MAX_ATTEMPTS: 3,\n    BACKOFF_MULTIPLIER: 2,\n    INITIAL_DELAY_MS: 1000\n};\n/**\n * Configuración de seguridad\n */ const SECURITY_CONFIG = {\n    ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',\n    REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',\n    AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',\n    ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'\n};\n// ============================================================================\n// CONSTANTES DE MENSAJES Y TEXTOS\n// ============================================================================\n/**\n * Mensajes de error comunes\n */ const ERROR_MESSAGES = {\n    UNAUTHORIZED: 'No autorizado',\n    FORBIDDEN: 'Acceso denegado',\n    NOT_FOUND: 'Recurso no encontrado',\n    INTERNAL_ERROR: 'Error interno del servidor',\n    INVALID_DATA: 'Datos inválidos',\n    USER_NOT_FOUND: 'Usuario no encontrado',\n    PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',\n    PAYMENT_REQUIRED: 'Pago requerido',\n    LIMIT_EXCEEDED: 'Límite excedido',\n    FILE_TOO_LARGE: 'El archivo es demasiado grande',\n    INVALID_FILE_TYPE: 'Tipo de archivo no válido',\n    UPLOAD_FAILED: 'Error al subir el archivo',\n    PROCESSING_ERROR: 'Error al procesar la solicitud',\n    NETWORK_ERROR: 'Error de conexión',\n    TIMEOUT_ERROR: 'Tiempo de espera agotado'\n};\n/**\n * Mensajes de éxito\n */ const SUCCESS_MESSAGES = {\n    UPLOAD_SUCCESS: 'Archivo subido correctamente',\n    SAVE_SUCCESS: 'Guardado correctamente',\n    UPDATE_SUCCESS: 'Actualizado correctamente',\n    DELETE_SUCCESS: 'Eliminado correctamente',\n    PAYMENT_SUCCESS: 'Pago procesado correctamente',\n    REGISTRATION_SUCCESS: 'Registro completado',\n    LOGIN_SUCCESS: 'Sesión iniciada',\n    LOGOUT_SUCCESS: 'Sesión cerrada',\n    PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',\n    EMAIL_SENT: 'Email enviado correctamente'\n};\n/**\n * Mensajes de validación\n */ const VALIDATION_MESSAGES = {\n    REQUIRED_FIELD: 'Este campo es obligatorio',\n    INVALID_EMAIL: 'Email no válido',\n    PASSWORD_TOO_SHORT: `La contraseña debe tener al menos ${TEXT_LIMITS.MIN_PASSWORD_LENGTH} caracteres`,\n    PASSWORD_TOO_LONG: `La contraseña no puede tener más de ${TEXT_LIMITS.MAX_PASSWORD_LENGTH} caracteres`,\n    PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',\n    INVALID_FILE_SIZE: `El archivo no puede superar ${FILE_LIMITS.MAX_SIZE_MB}MB`,\n    INVALID_FILE_FORMAT: 'Formato de archivo no válido',\n    TEXT_TOO_SHORT: `El texto debe tener al menos ${TEXT_LIMITS.MIN_CONTENT_LENGTH} caracteres`,\n    TEXT_TOO_LONG: `El texto no puede superar ${TEXT_LIMITS.MAX_CONTENT_LENGTH} caracteres`\n};\n// ============================================================================\n// CONSTANTES DE ESTADO Y TIPOS\n// ============================================================================\n/**\n * Estados de procesamiento\n */ const PROCESSING_STATES = {\n    IDLE: 'idle',\n    LOADING: 'loading',\n    PROCESSING: 'processing',\n    SUCCESS: 'success',\n    ERROR: 'error',\n    CANCELLED: 'cancelled'\n};\n/**\n * Estados de pago\n */ const PAYMENT_STATES = {\n    PENDING: 'pending',\n    PROCESSING: 'processing',\n    COMPLETED: 'completed',\n    FAILED: 'failed',\n    CANCELLED: 'cancelled',\n    REFUNDED: 'refunded'\n};\n/**\n * Tipos de notificación\n */ const NOTIFICATION_TYPES = {\n    INFO: 'info',\n    SUCCESS: 'success',\n    WARNING: 'warning',\n    ERROR: 'error'\n};\n/**\n * Niveles de severidad\n */ const SEVERITY_LEVELS = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n};\n// ============================================================================\n// CONSTANTES DE CONFIGURACIÓN DE ENTORNO\n// ============================================================================\n/**\n * Variables de entorno requeridas\n */ const REQUIRED_ENV_VARS = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'SUPABASE_SERVICE_ROLE_KEY',\n    'STRIPE_SECRET_KEY',\n    'STRIPE_WEBHOOK_SECRET',\n    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'\n];\n/**\n * Configuración de automatización\n */ const AUTOMATION_CONFIG = {\n    INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),\n    DEFAULT_TRIAL_DAYS: 5,\n    DEFAULT_GRACE_PERIOD_DAYS: 3\n};\n// ============================================================================\n// CONSTANTES DE BUSINESS LOGIC\n// ============================================================================\n/**\n * Costos y precios (en centavos)\n */ const PRICING = {\n    ADDITIONAL_TOKENS_PRICE: 1000,\n    ADDITIONAL_TOKENS_AMOUNT: 1000000,\n    FREE_PLAN_PRICE: 0,\n    BASIC_PLAN_PRICE: 999,\n    PRO_PLAN_PRICE: 1999 // €19.99\n};\n/**\n * Límites de planes gratuitos\n */ const FREE_PLAN_LIMITS = {\n    DOCUMENTS: 1,\n    MIND_MAPS_TRIAL: 2,\n    TESTS_TRIAL: 10,\n    FLASHCARDS_TRIAL: 10,\n    TOKENS_TRIAL: 50000,\n    TRIAL_DAYS: 5\n};\n/**\n * Factores de riesgo de seguridad\n */ const SECURITY_RISK_SCORES = {\n    MISSING_USER_AGENT: 30,\n    BOT_USER_AGENT: 20,\n    EXTERNAL_REFERER: 10,\n    SUSPICIOUS_PATTERN: 25,\n    HIGH_FREQUENCY: 40\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/features.ts":
/*!********************************!*\
  !*** ./src/config/features.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* binding */ ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* binding */ ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* binding */ ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   FEATURES_CONFIG: () => (/* binding */ FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* binding */ FEATURE_IDS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* binding */ PLAN_RESTRICTED_ROUTES),\n/* harmony export */   actionToFeature: () => (/* binding */ actionToFeature),\n/* harmony export */   activityToFeature: () => (/* binding */ activityToFeature),\n/* harmony export */   featureRequiresPayment: () => (/* binding */ featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* binding */ getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* binding */ getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* binding */ getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* binding */ getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* binding */ getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* binding */ getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* binding */ getFeaturesForPlan),\n/* harmony export */   isValidFeatureId: () => (/* binding */ isValidFeatureId)\n/* harmony export */ });\n// src/config/features.ts\n// Configuración centralizada de características y funcionalidades\n// ============================================================================\n// CONSTANTES DE FEATURES\n// ============================================================================\n/**\n * Identificadores únicos de características del sistema\n */ const FEATURE_IDS = {\n    DOCUMENT_UPLOAD: 'document_upload',\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_TUTOR_CHAT: 'ai_tutor_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_A1_A2: 'summary_a1_a2'\n};\n/**\n * Acciones que pueden realizarse en el sistema\n */ const ACTION_TYPES = {\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_CHAT: 'ai_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_GENERATION: 'summary_generation'\n};\n/**\n * Configuración de todas las características del sistema\n */ const FEATURES_CONFIG = {\n    [FEATURE_IDS.DOCUMENT_UPLOAD]: {\n        id: FEATURE_IDS.DOCUMENT_UPLOAD,\n        name: 'document_upload',\n        displayName: 'Subida de documentos',\n        description: 'Permite subir y procesar documentos PDF para estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 0,\n        icon: 'FiUpload',\n        route: '/app'\n    },\n    [FEATURE_IDS.TEST_GENERATION]: {\n        id: FEATURE_IDS.TEST_GENERATION,\n        name: 'test_generation',\n        displayName: 'Generación de tests',\n        description: 'Genera tests automáticos basados en el contenido de estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 5000,\n        icon: 'FiFileText',\n        route: '/app/tests'\n    },\n    [FEATURE_IDS.FLASHCARD_GENERATION]: {\n        id: FEATURE_IDS.FLASHCARD_GENERATION,\n        name: 'flashcard_generation',\n        displayName: 'Generación de flashcards',\n        description: 'Crea flashcards inteligentes para memorización efectiva',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 3000,\n        icon: 'FiLayers',\n        route: '/app/flashcards'\n    },\n    [FEATURE_IDS.MIND_MAP_GENERATION]: {\n        id: FEATURE_IDS.MIND_MAP_GENERATION,\n        name: 'mind_map_generation',\n        displayName: 'Generación de mapas mentales',\n        description: 'Genera mapas mentales visuales para mejor comprensión',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 4000,\n        icon: 'FiGitBranch',\n        route: '/app/mindmaps'\n    },\n    [FEATURE_IDS.AI_TUTOR_CHAT]: {\n        id: FEATURE_IDS.AI_TUTOR_CHAT,\n        name: 'ai_tutor_chat',\n        displayName: 'Chat con preparador IA',\n        description: 'Interactúa con un preparador de oposiciones inteligente',\n        category: 'premium',\n        minimumPlans: [\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 2000,\n        icon: 'FiMessageSquare',\n        route: '/app/ai-tutor'\n    },\n    [FEATURE_IDS.STUDY_PLANNING]: {\n        id: FEATURE_IDS.STUDY_PLANNING,\n        name: 'study_planning',\n        displayName: 'Planificación de estudios',\n        description: 'Crea planes de estudio personalizados y estructurados',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 20000,\n        icon: 'FiCalendar',\n        route: '/plan-estudios'\n    },\n    [FEATURE_IDS.SUMMARY_A1_A2]: {\n        id: FEATURE_IDS.SUMMARY_A1_A2,\n        name: 'summary_a1_a2',\n        displayName: 'Resúmenes A1 y A2',\n        description: 'Genera resúmenes especializados para oposiciones A1 y A2',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 6000,\n        icon: 'FiBook',\n        route: '/app/summaries'\n    }\n};\n// ============================================================================\n// MAPEOS Y UTILIDADES\n// ============================================================================\n/**\n * Mapeo de acciones a características\n */ const ACTION_TO_FEATURE_MAP = {\n    [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,\n    [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,\n    [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,\n    [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,\n    [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,\n    [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2\n};\n/**\n * Mapeo de actividades de tokens a características\n */ const ACTIVITY_TO_FEATURE_MAP = {\n    'test_generation': FEATURE_IDS.TEST_GENERATION,\n    'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,\n    'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,\n    'ai_tutor_chat': FEATURE_IDS.AI_TUTOR_CHAT,\n    'study_planning': FEATURE_IDS.STUDY_PLANNING,\n    'summary_a1_a2': FEATURE_IDS.SUMMARY_A1_A2,\n    'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD\n};\n/**\n * Configuración de rutas restringidas por plan\n */ const PLAN_RESTRICTED_ROUTES = {\n    '/plan-estudios': [\n        'pro'\n    ],\n    '/app/ai-tutor': [\n        'usuario',\n        'pro'\n    ],\n    '/app/summaries': [\n        'pro'\n    ],\n    '/app/advanced-features': [\n        'pro'\n    ]\n};\n// ============================================================================\n// FUNCIONES UTILITARIAS\n// ============================================================================\n/**\n * Obtiene la configuración de una característica\n */ function getFeatureConfig(featureId) {\n    return FEATURES_CONFIG[featureId];\n}\n/**\n * Obtiene el nombre para mostrar de una característica\n */ function getFeatureDisplayName(featureId) {\n    const config = FEATURES_CONFIG[featureId];\n    return config?.displayName || featureId;\n}\n/**\n * Obtiene todas las características de una categoría\n */ function getFeaturesByCategory(category) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.category === category);\n}\n/**\n * Obtiene las características disponibles para un plan\n */ function getFeaturesForPlan(planId) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.minimumPlans.includes(planId));\n}\n/**\n * Verifica si una característica requiere pago\n */ function featureRequiresPayment(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.requiresPayment || false;\n}\n/**\n * Obtiene los tokens requeridos para una característica\n */ function getFeatureTokensRequired(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.tokensRequired || 0;\n}\n/**\n * Convierte una acción a su característica correspondiente\n */ function actionToFeature(action) {\n    return ACTION_TO_FEATURE_MAP[action];\n}\n/**\n * Convierte una actividad a su característica correspondiente\n */ function activityToFeature(activity) {\n    return ACTIVITY_TO_FEATURE_MAP[activity];\n}\n/**\n * Obtiene todas las características como array\n */ function getAllFeatures() {\n    return Object.values(FEATURES_CONFIG);\n}\n/**\n * Obtiene los IDs de todas las características\n */ function getAllFeatureIds() {\n    return Object.keys(FEATURES_CONFIG);\n}\n/**\n * Verifica si un ID de característica es válido\n */ function isValidFeatureId(featureId) {\n    return featureId in FEATURES_CONFIG;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/features.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/index.ts":
/*!*****************************!*\
  !*** ./src/config/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES),\n/* harmony export */   FEATURES_CONFIG: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURE_IDS),\n/* harmony export */   FILE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FREE_PLAN_LIMITS),\n/* harmony export */   LIMITS: () => (/* binding */ LIMITS),\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PAYMENT_STATES),\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.PLAN_RESTRICTED_ROUTES),\n/* harmony export */   PRICING: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG),\n/* harmony export */   SECURITY: () => (/* binding */ SECURITY),\n/* harmony export */   SECURITY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS),\n/* harmony export */   TIME_CONFIG: () => (/* binding */ TIME_CONFIG),\n/* harmony export */   TOKEN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES),\n/* harmony export */   actionToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.actionToFeature),\n/* harmony export */   activityToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.activityToFeature),\n/* harmony export */   canPerformAction: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.checkUserFeatureAccess),\n/* harmony export */   featureRequiresPayment: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesForPlan),\n/* harmony export */   getPlanConfiguration: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.isUnlimited),\n/* harmony export */   isValidFeatureId: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.isValidFeatureId)\n/* harmony export */ });\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/config/plans.ts\");\n/* harmony import */ var _features__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./features */ \"(rsc)/./src/config/features.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./src/config/constants.ts\");\n/**\n * Configuración centralizada - Re-exports\n * \n * Este archivo centraliza todas las configuraciones del sistema,\n * proporcionando un punto único de importación para cualquier configuración.\n * \n * Uso:\n * import { PLAN_CONFIGURATIONS, FEATURES_CONFIG, ERROR_MESSAGES } from '@/config';\n */ // ============================================================================\n// CONFIGURACIONES DE PLANES\n// ============================================================================\n\n// ============================================================================\n// CONFIGURACIONES DE FEATURES\n// ============================================================================\n\n// ============================================================================\n// CONSTANTES DEL SISTEMA\n// ============================================================================\n\n// ============================================================================\n// RE-EXPORTS COMBINADOS PARA CONVENIENCIA\n// ============================================================================\n// Importar constantes para uso en re-exports combinados\n\n/**\n * Todas las configuraciones de límites en un solo objeto\n */ const LIMITS = {\n    FILE: _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS,\n    TEXT: _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS,\n    TOKEN: _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS,\n    RATE: _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS\n};\n/**\n * Todas las configuraciones de tiempo en un solo objeto\n */ const TIME_CONFIG = {\n    TIMEOUTS: _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS,\n    RETRY: _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG\n};\n/**\n * Todos los mensajes del sistema en un solo objeto\n */ const MESSAGES = {\n    ERROR: _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES,\n    SUCCESS: _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES,\n    VALIDATION: _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES\n};\n/**\n * Todas las configuraciones de seguridad en un solo objeto\n */ const SECURITY = {\n    CONFIG: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG,\n    RISK_SCORES: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29uZmlnL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7Ozs7OztDQVFDLEdBRUQsK0VBQStFO0FBQy9FLDRCQUE0QjtBQUM1QiwrRUFBK0U7QUFtQjlEO0FBRWpCLCtFQUErRTtBQUMvRSw4QkFBOEI7QUFDOUIsK0VBQStFO0FBOEIzRDtBQUVwQiwrRUFBK0U7QUFDL0UseUJBQXlCO0FBQ3pCLCtFQUErRTtBQW1DMUQ7QUFFckIsK0VBQStFO0FBQy9FLDBDQUEwQztBQUMxQywrRUFBK0U7QUFFL0Usd0RBQXdEO0FBYW5DO0FBRXJCOztDQUVDLEdBQ00sTUFBTStDLFNBQVM7SUFDcEJDLE1BQU1uQixtREFBV0E7SUFDakJvQixNQUFNbkIsbURBQVdBO0lBQ2pCb0IsT0FBT25CLG9EQUFZQTtJQUNuQm9CLE1BQU1uQixtREFBV0E7QUFDbkIsRUFBVztBQUVYOztDQUVDLEdBQ00sTUFBTW9CLGNBQWM7SUFDekJuQixRQUFRQSxrREFBQUE7SUFDUm9CLE9BQU9uQixvREFBWUE7QUFDckIsRUFBVztBQUVYOztDQUVDLEdBQ00sTUFBTW9CLFdBQVc7SUFDdEJDLE9BQU9uQixzREFBY0E7SUFDckJvQixTQUFTbkIsd0RBQWdCQTtJQUN6Qm9CLFlBQVluQiwyREFBbUJBO0FBQ2pDLEVBQVc7QUFFWDs7Q0FFQyxHQUNNLE1BQU1vQixXQUFXO0lBQ3RCQyxRQUFReEIsdURBQWVBO0lBQ3ZCeUIsYUFBYWQsNERBQW9CQTtBQUNuQyxFQUFXIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MjZcXHNyY1xcY29uZmlnXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvbmZpZ3VyYWNpw7NuIGNlbnRyYWxpemFkYSAtIFJlLWV4cG9ydHNcbiAqIFxuICogRXN0ZSBhcmNoaXZvIGNlbnRyYWxpemEgdG9kYXMgbGFzIGNvbmZpZ3VyYWNpb25lcyBkZWwgc2lzdGVtYSxcbiAqIHByb3BvcmNpb25hbmRvIHVuIHB1bnRvIMO6bmljbyBkZSBpbXBvcnRhY2nDs24gcGFyYSBjdWFscXVpZXIgY29uZmlndXJhY2nDs24uXG4gKiBcbiAqIFVzbzpcbiAqIGltcG9ydCB7IFBMQU5fQ09ORklHVVJBVElPTlMsIEZFQVRVUkVTX0NPTkZJRywgRVJST1JfTUVTU0FHRVMgfSBmcm9tICdAL2NvbmZpZyc7XG4gKi9cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gQ09ORklHVVJBQ0lPTkVTIERFIFBMQU5FU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQge1xuICAvLyBJbnRlcmZhY2VzIHkgdGlwb3NcbiAgdHlwZSBQbGFuTGltaXRzLFxuICB0eXBlIFBsYW5Db25maWd1cmF0aW9uLFxuXG4gIC8vIENvbmZpZ3VyYWNpb25lcyBkZSBwbGFuZXNcbiAgUExBTl9DT05GSUdVUkFUSU9OUyxcblxuICAvLyBGdW5jaW9uZXMgdXRpbGl0YXJpYXNcbiAgZ2V0UGxhbkNvbmZpZ3VyYXRpb24sXG4gIGdldFRva2VuTGltaXRGb3JQbGFuLFxuICBoYXNGZWF0dXJlQWNjZXNzLFxuICBnZXRXZWVrbHlMaW1pdCxcbiAgZ2V0VHJpYWxMaW1pdCxcbiAgaXNVbmxpbWl0ZWQsXG4gIGNhblBlcmZvcm1BY3Rpb24sXG4gIGNoZWNrVXNlckZlYXR1cmVBY2Nlc3Ncbn0gZnJvbSAnLi9wbGFucyc7XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIENPTkZJR1VSQUNJT05FUyBERSBGRUFUVVJFU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQge1xuICAvLyBJbnRlcmZhY2VzIHkgdGlwb3NcbiAgdHlwZSBGZWF0dXJlSWQsXG4gIHR5cGUgRmVhdHVyZUNvbmZpZyxcbiAgdHlwZSBBY3Rpb25UeXBlLFxuXG4gIC8vIENvbnN0YW50ZXMgZGUgSURzXG4gIEZFQVRVUkVfSURTLFxuICBBQ1RJT05fVFlQRVMsXG5cbiAgLy8gQ29uZmlndXJhY2lvbmVzIGRlIGZlYXR1cmVzXG4gIEZFQVRVUkVTX0NPTkZJRyxcbiAgQUNUSVZJVFlfVE9fRkVBVFVSRV9NQVAsXG4gIEFDVElPTl9UT19GRUFUVVJFX01BUCxcbiAgUExBTl9SRVNUUklDVEVEX1JPVVRFUyxcblxuICAvLyBGdW5jaW9uZXMgdXRpbGl0YXJpYXNcbiAgZ2V0RmVhdHVyZUNvbmZpZyxcbiAgZ2V0RmVhdHVyZURpc3BsYXlOYW1lLFxuICBnZXRGZWF0dXJlc0J5Q2F0ZWdvcnksXG4gIGdldEZlYXR1cmVzRm9yUGxhbixcbiAgZmVhdHVyZVJlcXVpcmVzUGF5bWVudCxcbiAgZ2V0RmVhdHVyZVRva2Vuc1JlcXVpcmVkLFxuICBhY3Rpb25Ub0ZlYXR1cmUsXG4gIGFjdGl2aXR5VG9GZWF0dXJlLFxuICBnZXRBbGxGZWF0dXJlcyxcbiAgZ2V0QWxsRmVhdHVyZUlkcyxcbiAgaXNWYWxpZEZlYXR1cmVJZFxufSBmcm9tICcuL2ZlYXR1cmVzJztcblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gQ09OU1RBTlRFUyBERUwgU0lTVEVNQVxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQge1xuICAvLyBVUkxzIHkgcnV0YXNcbiAgQVBQX1VSTFMsXG4gIFBVQkxJQ19ST1VURVMsXG4gIEFVVEhFTlRJQ0FURURfUk9VVEVTLFxuICBcbiAgLy8gTMOtbWl0ZXMgZGUgYXJjaGl2b3MgeSB2YWxpZGFjacOzblxuICBGSUxFX0xJTUlUUyxcbiAgVEVYVF9MSU1JVFMsXG4gIFRPS0VOX0xJTUlUUyxcbiAgUkFURV9MSU1JVFMsXG4gIFxuICAvLyBUaW1lb3V0cyB5IGNvbmZpZ3VyYWNpw7NuXG4gIFRJTUVPVVRTLFxuICBSRVRSWV9DT05GSUcsXG4gIFNFQ1VSSVRZX0NPTkZJRyxcbiAgXG4gIC8vIE1lbnNhamVzIGRlbCBzaXN0ZW1hXG4gIEVSUk9SX01FU1NBR0VTLFxuICBTVUNDRVNTX01FU1NBR0VTLFxuICBWQUxJREFUSU9OX01FU1NBR0VTLFxuICBcbiAgLy8gRXN0YWRvcyB5IHRpcG9zIGRlbCBzaXN0ZW1hXG4gIFBST0NFU1NJTkdfU1RBVEVTLFxuICBQQVlNRU5UX1NUQVRFUyxcbiAgTk9USUZJQ0FUSU9OX1RZUEVTLFxuICBcbiAgLy8gQ29uZmlndXJhY2nDs24gZGUgZW50b3JubyB5IG5lZ29jaW9cbiAgUkVRVUlSRURfRU5WX1ZBUlMsXG4gIEFVVE9NQVRJT05fQ09ORklHLFxuICBQUklDSU5HLFxuICBGUkVFX1BMQU5fTElNSVRTLFxuICBTRUNVUklUWV9SSVNLX1NDT1JFU1xufSBmcm9tICcuL2NvbnN0YW50cyc7XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIFJFLUVYUE9SVFMgQ09NQklOQURPUyBQQVJBIENPTlZFTklFTkNJQVxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4vLyBJbXBvcnRhciBjb25zdGFudGVzIHBhcmEgdXNvIGVuIHJlLWV4cG9ydHMgY29tYmluYWRvc1xuaW1wb3J0IHtcbiAgRklMRV9MSU1JVFMsXG4gIFRFWFRfTElNSVRTLFxuICBUT0tFTl9MSU1JVFMsXG4gIFJBVEVfTElNSVRTLFxuICBUSU1FT1VUUyxcbiAgUkVUUllfQ09ORklHLFxuICBFUlJPUl9NRVNTQUdFUyxcbiAgU1VDQ0VTU19NRVNTQUdFUyxcbiAgVkFMSURBVElPTl9NRVNTQUdFUyxcbiAgU0VDVVJJVFlfQ09ORklHLFxuICBTRUNVUklUWV9SSVNLX1NDT1JFU1xufSBmcm9tICcuL2NvbnN0YW50cyc7XG5cbi8qKlxuICogVG9kYXMgbGFzIGNvbmZpZ3VyYWNpb25lcyBkZSBsw61taXRlcyBlbiB1biBzb2xvIG9iamV0b1xuICovXG5leHBvcnQgY29uc3QgTElNSVRTID0ge1xuICBGSUxFOiBGSUxFX0xJTUlUUyxcbiAgVEVYVDogVEVYVF9MSU1JVFMsXG4gIFRPS0VOOiBUT0tFTl9MSU1JVFMsXG4gIFJBVEU6IFJBVEVfTElNSVRTXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIFRvZGFzIGxhcyBjb25maWd1cmFjaW9uZXMgZGUgdGllbXBvIGVuIHVuIHNvbG8gb2JqZXRvXG4gKi9cbmV4cG9ydCBjb25zdCBUSU1FX0NPTkZJRyA9IHtcbiAgVElNRU9VVFMsXG4gIFJFVFJZOiBSRVRSWV9DT05GSUdcbn0gYXMgY29uc3Q7XG5cbi8qKlxuICogVG9kb3MgbG9zIG1lbnNhamVzIGRlbCBzaXN0ZW1hIGVuIHVuIHNvbG8gb2JqZXRvXG4gKi9cbmV4cG9ydCBjb25zdCBNRVNTQUdFUyA9IHtcbiAgRVJST1I6IEVSUk9SX01FU1NBR0VTLFxuICBTVUNDRVNTOiBTVUNDRVNTX01FU1NBR0VTLFxuICBWQUxJREFUSU9OOiBWQUxJREFUSU9OX01FU1NBR0VTXG59IGFzIGNvbnN0O1xuXG4vKipcbiAqIFRvZGFzIGxhcyBjb25maWd1cmFjaW9uZXMgZGUgc2VndXJpZGFkIGVuIHVuIHNvbG8gb2JqZXRvXG4gKi9cbmV4cG9ydCBjb25zdCBTRUNVUklUWSA9IHtcbiAgQ09ORklHOiBTRUNVUklUWV9DT05GSUcsXG4gIFJJU0tfU0NPUkVTOiBTRUNVUklUWV9SSVNLX1NDT1JFU1xufSBhcyBjb25zdDtcbiJdLCJuYW1lcyI6WyJQTEFOX0NPTkZJR1VSQVRJT05TIiwiZ2V0UGxhbkNvbmZpZ3VyYXRpb24iLCJnZXRUb2tlbkxpbWl0Rm9yUGxhbiIsImhhc0ZlYXR1cmVBY2Nlc3MiLCJnZXRXZWVrbHlMaW1pdCIsImdldFRyaWFsTGltaXQiLCJpc1VubGltaXRlZCIsImNhblBlcmZvcm1BY3Rpb24iLCJjaGVja1VzZXJGZWF0dXJlQWNjZXNzIiwiRkVBVFVSRV9JRFMiLCJBQ1RJT05fVFlQRVMiLCJGRUFUVVJFU19DT05GSUciLCJBQ1RJVklUWV9UT19GRUFUVVJFX01BUCIsIkFDVElPTl9UT19GRUFUVVJFX01BUCIsIlBMQU5fUkVTVFJJQ1RFRF9ST1VURVMiLCJnZXRGZWF0dXJlQ29uZmlnIiwiZ2V0RmVhdHVyZURpc3BsYXlOYW1lIiwiZ2V0RmVhdHVyZXNCeUNhdGVnb3J5IiwiZ2V0RmVhdHVyZXNGb3JQbGFuIiwiZmVhdHVyZVJlcXVpcmVzUGF5bWVudCIsImdldEZlYXR1cmVUb2tlbnNSZXF1aXJlZCIsImFjdGlvblRvRmVhdHVyZSIsImFjdGl2aXR5VG9GZWF0dXJlIiwiZ2V0QWxsRmVhdHVyZXMiLCJnZXRBbGxGZWF0dXJlSWRzIiwiaXNWYWxpZEZlYXR1cmVJZCIsIkFQUF9VUkxTIiwiUFVCTElDX1JPVVRFUyIsIkFVVEhFTlRJQ0FURURfUk9VVEVTIiwiRklMRV9MSU1JVFMiLCJURVhUX0xJTUlUUyIsIlRPS0VOX0xJTUlUUyIsIlJBVEVfTElNSVRTIiwiVElNRU9VVFMiLCJSRVRSWV9DT05GSUciLCJTRUNVUklUWV9DT05GSUciLCJFUlJPUl9NRVNTQUdFUyIsIlNVQ0NFU1NfTUVTU0FHRVMiLCJWQUxJREFUSU9OX01FU1NBR0VTIiwiUFJPQ0VTU0lOR19TVEFURVMiLCJQQVlNRU5UX1NUQVRFUyIsIk5PVElGSUNBVElPTl9UWVBFUyIsIlJFUVVJUkVEX0VOVl9WQVJTIiwiQVVUT01BVElPTl9DT05GSUciLCJQUklDSU5HIiwiRlJFRV9QTEFOX0xJTUlUUyIsIlNFQ1VSSVRZX1JJU0tfU0NPUkVTIiwiTElNSVRTIiwiRklMRSIsIlRFWFQiLCJUT0tFTiIsIlJBVEUiLCJUSU1FX0NPTkZJRyIsIlJFVFJZIiwiTUVTU0FHRVMiLCJFUlJPUiIsIlNVQ0NFU1MiLCJWQUxJREFUSU9OIiwiU0VDVVJJVFkiLCJDT05GSUciLCJSSVNLX1NDT1JFUyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/config/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 500000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 500000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe/config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.APP_URLS),\n/* harmony export */   PLANS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.PLANS),\n/* harmony export */   getPlanById: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.getPlanById),\n/* harmony export */   isValidPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.isValidPlan),\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/lib/stripe/plans.ts\");\n// src/lib/stripe/config.ts\n\n// Inicializar Stripe solo en el servidor\nconst stripe =  true ? new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2025-05-28.basil',\n    typescript: true\n}) : 0;\n// Importar configuración de planes\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS9jb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLDJCQUEyQjtBQUNDO0FBRTVCLHlDQUF5QztBQUNsQyxNQUFNQyxTQUFTLEtBQTZCLEdBQy9DLElBQUlELDhDQUFNQSxDQUFDRSxRQUFRQyxHQUFHLENBQUNDLGlCQUFpQixFQUFHO0lBQ3pDQyxZQUFZO0lBQ1pDLFlBQVk7QUFDZCxLQUNBLENBQUksQ0FBQztBQUVULG1DQUFtQztBQUNpQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjI2XFxzcmNcXGxpYlxcc3RyaXBlXFxjb25maWcudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2xpYi9zdHJpcGUvY29uZmlnLnRzXG5pbXBvcnQgU3RyaXBlIGZyb20gJ3N0cmlwZSc7XG5cbi8vIEluaWNpYWxpemFyIFN0cmlwZSBzb2xvIGVuIGVsIHNlcnZpZG9yXG5leHBvcnQgY29uc3Qgc3RyaXBlID0gdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCdcbiAgPyBuZXcgU3RyaXBlKHByb2Nlc3MuZW52LlNUUklQRV9TRUNSRVRfS0VZISwge1xuICAgICAgYXBpVmVyc2lvbjogJzIwMjUtMDUtMjguYmFzaWwnLFxuICAgICAgdHlwZXNjcmlwdDogdHJ1ZSxcbiAgICB9KVxuICA6IG51bGw7XG5cbi8vIEltcG9ydGFyIGNvbmZpZ3VyYWNpw7NuIGRlIHBsYW5lc1xuZXhwb3J0IHsgUExBTlMsIGdldFBsYW5CeUlkLCBpc1ZhbGlkUGxhbiwgQVBQX1VSTFMgfSBmcm9tICcuL3BsYW5zJztcbmV4cG9ydCB0eXBlIHsgUGxhbklkIH0gZnJvbSAnLi9wbGFucyc7XG4iXSwibmFtZXMiOlsiU3RyaXBlIiwic3RyaXBlIiwicHJvY2VzcyIsImVudiIsIlNUUklQRV9TRUNSRVRfS0VZIiwiYXBpVmVyc2lvbiIsInR5cGVzY3JpcHQiLCJQTEFOUyIsImdldFBsYW5CeUlkIiwiaXNWYWxpZFBsYW4iLCJBUFBfVVJMUyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/config */ \"(rsc)/./src/config/index.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 500.000 tokens.',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de resúmenes para el ejercicio de desarrollo para cuerpos superiores A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens.'\n        ],\n        limits: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _config__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return plan?.planConfig || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        stripePriceId: 'price_tokens_additional'\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: `${\"http://localhost:3000\"}/thank-you`,\n    cancel: `${\"http://localhost:3000\"}/upgrade-plan`,\n    webhook: `${\"http://localhost:3000\"}/api/stripe/webhook`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS9wbGFucy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUEsMEJBQTBCO0FBQzFCLDJEQUEyRDtBQUVaO0FBRXhDLE1BQU1DLFFBQVE7SUFDbkJDLE1BQU07UUFDSkMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsaUJBQWlCO1FBQ2pCQyxlQUFlO1FBQ2ZDLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVFULHdEQUFtQkEsQ0FBQ0UsSUFBSSxDQUFDTyxNQUFNO1FBQ3ZDQyxZQUFZVix3REFBbUJBLENBQUNFLElBQUk7SUFDdEM7SUFDQVMsU0FBUztRQUNQUixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakIsOEVBQThFO1FBQzlFLHdGQUF3RjtRQUN4RkMsZUFBZTtRQUNmQyxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVFULHdEQUFtQkEsQ0FBQ1csT0FBTyxDQUFDRixNQUFNO1FBQzFDQyxZQUFZVix3REFBbUJBLENBQUNXLE9BQU87SUFDekM7SUFDQUMsS0FBSztRQUNIVCxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZkMsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUVQsd0RBQW1CQSxDQUFDWSxHQUFHLENBQUNILE1BQU07UUFDdENDLFlBQVlWLHdEQUFtQkEsQ0FBQ1ksR0FBRztJQUNyQztBQUNGLEVBQVc7QUFJWCxtQ0FBbUM7QUFDNUIsU0FBU0MsWUFBWUMsTUFBYztJQUN4QyxPQUFPYixLQUFLLENBQUNhLE9BQWlCLElBQUk7QUFDcEM7QUFFQSw0Q0FBNEM7QUFDckMsU0FBU0MsWUFBWUQsTUFBYztJQUN4QyxPQUFPQSxVQUFVYjtBQUNuQjtBQUVBLHVEQUF1RDtBQUNoRCxTQUFTZSxrQkFBa0JGLE1BQWM7SUFDOUMsTUFBTUcsT0FBT0osWUFBWUM7SUFDekIsT0FBT0csTUFBTVAsY0FBYztBQUM3QjtBQUVBLHlDQUF5QztBQUNsQyxNQUFNUSxzQkFBc0I7SUFDakNDLFFBQVE7UUFDTmhCLElBQUk7UUFDSkMsTUFBTTtRQUNOZ0IsYUFBYTtRQUNiZixPQUFPO1FBQ1BnQixhQUFhO1FBQ2IsK0NBQStDO1FBQy9DZixpQkFBaUI7UUFDakJDLGVBQWU7SUFDakI7QUFDRixFQUFXO0FBRVgsd0JBQXdCO0FBQ2pCLE1BQU1lLFdBQVc7SUFDdEJDLFNBQVMsR0FBR0MsdUJBQStCLENBQUMsVUFBVSxDQUFDO0lBQ3ZERyxRQUFRLEdBQUdILHVCQUErQixDQUFDLGFBQWEsQ0FBQztJQUN6REksU0FBUyxHQUFHSix1QkFBK0IsQ0FBQyxtQkFBbUIsQ0FBQztBQUNsRSxFQUFXIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MjZcXHNyY1xcbGliXFxzdHJpcGVcXHBsYW5zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9saWIvc3RyaXBlL3BsYW5zLnRzXG4vLyBDb25maWd1cmFjacOzbiBkZSBwbGFuZXMgaW50ZWdyYWRhIGNvbiBzaXN0ZW1hIGRlIGzDrW1pdGVzXG5cbmltcG9ydCB7IFBMQU5fQ09ORklHVVJBVElPTlMgfSBmcm9tICdAL2NvbmZpZyc7XG5cbmV4cG9ydCBjb25zdCBQTEFOUyA9IHtcbiAgZnJlZToge1xuICAgIGlkOiAnZnJlZScsXG4gICAgbmFtZTogJ1BsYW4gR3JhdGlzJyxcbiAgICBwcmljZTogMCxcbiAgICBzdHJpcGVQcm9kdWN0SWQ6IG51bGwsXG4gICAgc3RyaXBlUHJpY2VJZDogbnVsbCxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ0luY2x1eWU6JyxcbiAgICAgICfigKIgVXNvIGRlIGxhIHBsYXRhZm9ybWEgc29sbyBkdXJhbnRlIDUgZMOtYXMnLFxuICAgICAgJ+KAoiBTdWJpZGEgZGUgZG9jdW1lbnRvczogbcOheGltbyAxIGRvY3VtZW50bycsXG4gICAgICAn4oCiIEdlbmVyYWRvciBkZSB0ZXN0OiBtw6F4aW1vIDEwIHByZWd1bnRhcyB0ZXN0JyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIGZsYXNoY2FyZHM6IG3DoXhpbW8gMTAgdGFyamV0YXMgZmxhc2hjYXJkJyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIG1hcGFzIG1lbnRhbGVzOiBtw6F4aW1vIDIgbWFwYXMgbWVudGFsZXMnLFxuICAgICAgJ05vIGluY2x1eWU6JyxcbiAgICAgICfigKIgUGxhbmlmaWNhY2nDs24gZGUgZXN0dWRpb3MnLFxuICAgICAgJ+KAoiBIYWJsYSBjb24gdHUgcHJlcGFyYWRvciBJQScsXG4gICAgICAn4oCiIFJlc8O6bWVuZXMgcGFyYSBlbCBlamVyY2ljaW8gZGUgZGVzYXJyb2xsbyBwYXJhIGN1ZXJwb3Mgc3VwZXJpb3JlcyBBMiB5IEExJyxcbiAgICBdLFxuICAgIGxpbWl0czogUExBTl9DT05GSUdVUkFUSU9OUy5mcmVlLmxpbWl0cyxcbiAgICBwbGFuQ29uZmlnOiBQTEFOX0NPTkZJR1VSQVRJT05TLmZyZWVcbiAgfSxcbiAgdXN1YXJpbzoge1xuICAgIGlkOiAndXN1YXJpbycsXG4gICAgbmFtZTogJ1BsYW4gVXN1YXJpbycsXG4gICAgcHJpY2U6IDEwMDAsIC8vIEVuIGNlbnRhdm9zICjigqwxMC4wMClcbiAgICBzdHJpcGVQcm9kdWN0SWQ6ICdwcm9kX1NSNjVCZEtkZWsxT1hkJyxcbiAgICAvLyBJTVBPUlRBTlRFOiBFc3RlIHByZWNpbyBkZWJlIHNlciByZWN1cnJlbnRlIChzdXNjcmlwY2nDs24gbWVuc3VhbCkgZW4gU3RyaXBlXG4gICAgLy8gU2kgYWN0dWFsbWVudGUgZXMgdW4gcGFnbyDDum5pY28sIGNyZWFyIHVuIG51ZXZvIHByZWNpbyByZWN1cnJlbnRlIGVuIFN0cmlwZSBEYXNoYm9hcmRcbiAgICBzdHJpcGVQcmljZUlkOiAncHJpY2VfMVJhZTU4MDdrRm4zc0lYaFJmM2FkWDFuJyxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ0luY2x1eWU6JyxcbiAgICAgICfigKIgVXNvIGRlIGxhIHBsYXRhZm9ybWEgZHVyYW50ZSBlbCBtZXMgKHVuYSB2ZXogZmluYWxpemFkbyBubyBwb2Ryw6Egdm9sdmVyIGEgYWNjZWRlciBoYXN0YSByZW5vdmFjacOzbiknLFxuICAgICAgJ+KAoiBTdWJpZGEgZGUgZG9jdW1lbnRvcycsXG4gICAgICAn4oCiIEhhYmxhIGNvbiB0dSBwcmVwYXJhZG9yIElBIConLFxuICAgICAgJ+KAoiBHZW5lcmFkb3IgZGUgdGVzdCAqJyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIGZsYXNoY2FyZHMgKicsXG4gICAgICAn4oCiIEdlbmVyYWRvciBkZSBtYXBhcyBtZW50YWxlcyAqJyxcbiAgICAgICfigKIgKiBQYXJhIGxhcyB0YXJlYXMgZW4gbGFzIHF1ZSBzZSBoYWdhIHVzbyBkZSBJQSwgZWwgbMOtbWl0ZSBtZW5zdWFsIHNlcsOhIGRlIDUwMC4wMDAgdG9rZW5zLicsXG4gICAgICAnTm8gaW5jbHV5ZTonLFxuICAgICAgJ+KAoiBQbGFuaWZpY2FjacOzbiBkZSBlc3R1ZGlvcycsXG4gICAgICAn4oCiIFJlc8O6bWVuZXMgcGFyYSBlbCBlamVyY2ljaW8gZGUgZGVzYXJyb2xsbyBwYXJhIGN1ZXJwb3Mgc3VwZXJpb3JlcyBBMiB5IEExJyxcbiAgICBdLFxuICAgIGxpbWl0czogUExBTl9DT05GSUdVUkFUSU9OUy51c3VhcmlvLmxpbWl0cyxcbiAgICBwbGFuQ29uZmlnOiBQTEFOX0NPTkZJR1VSQVRJT05TLnVzdWFyaW9cbiAgfSxcbiAgcHJvOiB7XG4gICAgaWQ6ICdwcm8nLFxuICAgIG5hbWU6ICdQbGFuIFBybycsXG4gICAgcHJpY2U6IDE1MDAsIC8vIEVuIGNlbnRhdm9zICjigqwxNS4wMClcbiAgICBzdHJpcGVQcm9kdWN0SWQ6ICdwcm9kX1NSNjZVMkc3YlZKcXUzJyxcbiAgICBzdHJpcGVQcmljZUlkOiAncHJpY2VfMVJhZTNVMDdrRm4zc0lYaGt2U3VKY28xJyxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ0luY2x1eWU6JyxcbiAgICAgICfigKIgVXNvIGRlIGxhIHBsYXRhZm9ybWEgZHVyYW50ZSBlbCBtZXMgKHVuYSB2ZXogZmluYWxpemFkbyBubyBwb2Ryw6Egdm9sdmVyIGEgYWNjZWRlciBoYXN0YSByZW5vdmFjacOzbiknLFxuICAgICAgJ+KAoiBTdWJpZGEgZGUgZG9jdW1lbnRvcycsXG4gICAgICAn4oCiIFBsYW5pZmljYWNpw7NuIGRlIGVzdHVkaW9zIG1lZGlhbnRlIElBKicsXG4gICAgICAn4oCiIEhhYmxhIGNvbiB0dSBwcmVwYXJhZG9yIElBIConLFxuICAgICAgJ+KAoiBHZW5lcmFkb3IgZGUgdGVzdCAqJyxcbiAgICAgICfigKIgR2VuZXJhZG9yIGRlIGZsYXNoY2FyZHMgKicsXG4gICAgICAn4oCiIEdlbmVyYWRvciBkZSBtYXBhcyBtZW50YWxlcyAqJyxcbiAgICAgICfigKIgR2VuZXJhY2nDs24gZGUgcmVzw7ptZW5lcyBwYXJhIGVsIGVqZXJjaWNpbyBkZSBkZXNhcnJvbGxvIHBhcmEgY3VlcnBvcyBzdXBlcmlvcmVzIEEyIHkgQTEnLFxuICAgICAgJ+KAoiAqIFBhcmEgbGFzIHRhcmVhcyBlbiBsYXMgcXVlIHNlIGhhZ2EgdXNvIGRlIElBLCBlbCBsw61taXRlIG1lbnN1YWwgc2Vyw6EgZGUgMS4wMDAuMDAwIGRlIHRva2Vucy4nLFxuICAgIF0sXG4gICAgbGltaXRzOiBQTEFOX0NPTkZJR1VSQVRJT05TLnByby5saW1pdHMsXG4gICAgcGxhbkNvbmZpZzogUExBTl9DT05GSUdVUkFUSU9OUy5wcm9cbiAgfVxufSBhcyBjb25zdDtcblxuZXhwb3J0IHR5cGUgUGxhbklkID0ga2V5b2YgdHlwZW9mIFBMQU5TO1xuXG4vLyBGdW5jacOzbiBwYXJhIG9idGVuZXIgcGxhbiBwb3IgSURcbmV4cG9ydCBmdW5jdGlvbiBnZXRQbGFuQnlJZChwbGFuSWQ6IHN0cmluZyk6IHR5cGVvZiBQTEFOU1tQbGFuSWRdIHwgbnVsbCB7XG4gIHJldHVybiBQTEFOU1twbGFuSWQgYXMgUGxhbklkXSB8fCBudWxsO1xufVxuXG4vLyBGdW5jacOzbiBwYXJhIHZhbGlkYXIgc2kgdW4gcGxhbiBlcyB2w6FsaWRvXG5leHBvcnQgZnVuY3Rpb24gaXNWYWxpZFBsYW4ocGxhbklkOiBzdHJpbmcpOiBwbGFuSWQgaXMgUGxhbklkIHtcbiAgcmV0dXJuIHBsYW5JZCBpbiBQTEFOUztcbn1cblxuLy8gRnVuY2nDs24gcGFyYSBvYnRlbmVyIGNvbmZpZ3VyYWNpw7NuIGNvbXBsZXRhIGRlbCBwbGFuXG5leHBvcnQgZnVuY3Rpb24gZ2V0RnVsbFBsYW5Db25maWcocGxhbklkOiBzdHJpbmcpIHtcbiAgY29uc3QgcGxhbiA9IGdldFBsYW5CeUlkKHBsYW5JZCk7XG4gIHJldHVybiBwbGFuPy5wbGFuQ29uZmlnIHx8IG51bGw7XG59XG5cbi8vIENvbmZpZ3VyYWNpw7NuIGRlIHByb2R1Y3RvcyBhZGljaW9uYWxlc1xuZXhwb3J0IGNvbnN0IEFERElUSU9OQUxfUFJPRFVDVFMgPSB7XG4gIHRva2Vuczoge1xuICAgIGlkOiAndG9rZW5zJyxcbiAgICBuYW1lOiAnVG9rZW5zIEFkaWNpb25hbGVzJyxcbiAgICBkZXNjcmlwdGlvbjogJzEsMDAwLDAwMCB0b2tlbnMgYWRpY2lvbmFsZXMgcGFyYSB0dSBjdWVudGEnLFxuICAgIHByaWNlOiAxMDAwLCAvLyBFbiBjZW50YXZvcyAo4oKsMTAuMDApXG4gICAgdG9rZW5BbW91bnQ6IDEwMDAwMDAsXG4gICAgLy8gRXN0b3MgSURzIHNlIGRlYmVuIGNyZWFyIGVuIFN0cmlwZSBEYXNoYm9hcmRcbiAgICBzdHJpcGVQcm9kdWN0SWQ6ICdwcm9kX3Rva2Vuc19hZGRpdGlvbmFsJywgLy8gUGxhY2Vob2xkZXIgLSBjcmVhciBlbiBTdHJpcGVcbiAgICBzdHJpcGVQcmljZUlkOiAncHJpY2VfdG9rZW5zX2FkZGl0aW9uYWwnLCAvLyBQbGFjZWhvbGRlciAtIGNyZWFyIGVuIFN0cmlwZVxuICB9XG59IGFzIGNvbnN0O1xuXG4vLyBVUkxzIGRlIGxhIGFwbGljYWNpw7NuXG5leHBvcnQgY29uc3QgQVBQX1VSTFMgPSB7XG4gIHN1Y2Nlc3M6IGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkx9L3RoYW5rLXlvdWAsXG4gIGNhbmNlbDogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBQX1VSTH0vdXBncmFkZS1wbGFuYCxcbiAgd2ViaG9vazogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBQX1VSTH0vYXBpL3N0cmlwZS93ZWJob29rYCxcbn0gYXMgY29uc3Q7XG4iXSwibmFtZXMiOlsiUExBTl9DT05GSUdVUkFUSU9OUyIsIlBMQU5TIiwiZnJlZSIsImlkIiwibmFtZSIsInByaWNlIiwic3RyaXBlUHJvZHVjdElkIiwic3RyaXBlUHJpY2VJZCIsImZlYXR1cmVzIiwibGltaXRzIiwicGxhbkNvbmZpZyIsInVzdWFyaW8iLCJwcm8iLCJnZXRQbGFuQnlJZCIsInBsYW5JZCIsImlzVmFsaWRQbGFuIiwiZ2V0RnVsbFBsYW5Db25maWciLCJwbGFuIiwiQURESVRJT05BTF9QUk9EVUNUUyIsInRva2VucyIsImRlc2NyaXB0aW9uIiwidG9rZW5BbW91bnQiLCJBUFBfVVJMUyIsInN1Y2Nlc3MiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBQX1VSTCIsImNhbmNlbCIsIndlYmhvb2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/plans.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/function-bind","vendor-chunks/side-channel-map","vendor-chunks/side-channel","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();