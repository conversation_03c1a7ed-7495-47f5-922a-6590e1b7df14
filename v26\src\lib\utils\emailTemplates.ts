// src/lib/utils/emailTemplates.ts
// Plantillas de email personalizadas para OposiAI

import { getPlanConfiguration } from '@/config/plans';

export interface EmailTemplateData {
  customerName: string;
  customerEmail: string;
  planId: string;
  planName: string;
  inviteLink?: string;
  transactionId?: string;
  amount?: number;
  currency?: string;
}

export class EmailTemplates {
  
  /**
   * Email de bienvenida para nuevos usuarios
   */
  static getWelcomeEmail(data: EmailTemplateData): {
    subject: string;
    html: string;
    text: string;
  } {
    const planConfig = getPlanConfiguration(data.planId);
    const features = planConfig?.features || [];
    
    const subject = `🎉 ¡Bienvenido a OposiAI! Tu ${data.planName} está listo`;
    
    const html = `
      <!DOCTYPE html>
      <html lang="es">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bienvenido a OposiAI</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .plan-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
          .features { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .feature-item { display: flex; align-items: center; margin: 10px 0; }
          .feature-icon { color: #28a745; margin-right: 10px; font-weight: bold; }
          .cta-button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎉 ¡Bienvenido a OposiAI!</h1>
          <p>Tu cuenta ha sido activada exitosamente</p>
        </div>
        
        <div class="content">
          <h2>Hola ${data.customerName},</h2>
          
          <p>¡Gracias por unirte a OposiAI! Tu suscripción al <strong>${data.planName}</strong> ha sido activada y ya puedes comenzar a preparar tus oposiciones con la ayuda de nuestra inteligencia artificial.</p>
          
          <div class="plan-info">
            <h3>📋 Detalles de tu plan</h3>
            <p><strong>Plan:</strong> ${data.planName}</p>
            ${planConfig?.limits.monthlyTokens ? `<p><strong>Tokens mensuales:</strong> ${planConfig.limits.monthlyTokens.toLocaleString()}</p>` : ''}
            ${data.amount ? `<p><strong>Precio:</strong> ${(data.amount / 100).toFixed(2)}€</p>` : ''}
          </div>
          
          <div class="features">
            <h3>✨ Características incluidas</h3>
            ${features.map(feature => `
              <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>${getFeatureDisplayName(feature)}</span>
              </div>
            `).join('')}
          </div>
          
          <div style="text-align: center;">
            <h3>🚀 Completa tu registro</h3>
            <p>Para comenzar a usar OposiAI, necesitas configurar tu contraseña:</p>
            ${data.inviteLink ? `
              <a href="${data.inviteLink}" class="cta-button">
                Configurar mi contraseña
              </a>
            ` : ''}
            <p><small>Este enlace expira en 24 horas por seguridad.</small></p>
          </div>
          
          <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>📚 Primeros pasos recomendados</h3>
            <ol>
              <li><strong>Sube tus documentos:</strong> Comienza subiendo tus materiales de estudio en PDF o TXT</li>
              <li><strong>Genera contenido:</strong> Crea tests, flashcards y mapas mentales automáticamente</li>
              ${data.planId !== 'free' ? '<li><strong>Chatea con la IA:</strong> Haz preguntas específicas sobre tu temario</li>' : ''}
              ${data.planId === 'pro' ? '<li><strong>Planifica tu estudio:</strong> Crea un plan personalizado con IA</li>' : ''}
            </ol>
          </div>
        </div>
        
        <div class="footer">
          <p>¿Necesitas ayuda? Responde a este email o visita nuestro <a href="${process.env.NEXT_PUBLIC_APP_URL}/contact">centro de soporte</a></p>
          <p><small>OposiAI - Tu preparador de oposiciones con IA</small></p>
        </div>
      </body>
      </html>
    `;
    
    const text = `
¡Bienvenido a OposiAI, ${data.customerName}!

Tu suscripción al ${data.planName} ha sido activada exitosamente.

Detalles de tu plan:
- Plan: ${data.planName}
${planConfig?.limits.monthlyTokens ? `- Tokens mensuales: ${planConfig.limits.monthlyTokens.toLocaleString()}` : ''}
${data.amount ? `- Precio: ${(data.amount / 100).toFixed(2)}€` : ''}

Características incluidas:
${features.map(feature => `✓ ${getFeatureDisplayName(feature)}`).join('\n')}

Para comenzar a usar OposiAI, configura tu contraseña en:
${data.inviteLink || 'Enlace enviado por separado'}

Primeros pasos recomendados:
1. Sube tus documentos de estudio
2. Genera tests y flashcards automáticamente
${data.planId !== 'free' ? '3. Chatea con tu preparador IA' : ''}
${data.planId === 'pro' ? '4. Crea tu plan de estudios personalizado' : ''}

¿Necesitas ayuda? Responde a este email o visita ${process.env.NEXT_PUBLIC_APP_URL}/contact

¡Éxito en tus oposiciones!
El equipo de OposiAI
    `;
    
    return { subject, html, text };
  }
  
  /**
   * Email de confirmación de pago
   */
  static getPaymentConfirmationEmail(data: EmailTemplateData): {
    subject: string;
    html: string;
    text: string;
  } {
    const subject = `✅ Pago confirmado - ${data.planName} activado`;
    
    const html = `
      <!DOCTYPE html>
      <html lang="es">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Pago Confirmado</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .payment-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>✅ Pago Confirmado</h1>
          <p>Tu ${data.planName} ha sido activado</p>
        </div>
        
        <div class="content">
          <h2>Hola ${data.customerName},</h2>
          
          <p>Hemos recibido tu pago correctamente y tu plan ha sido activado de inmediato.</p>
          
          <div class="payment-details">
            <h3>💳 Detalles del pago</h3>
            <p><strong>Plan:</strong> ${data.planName}</p>
            ${data.amount ? `<p><strong>Importe:</strong> ${(data.amount / 100).toFixed(2)}€</p>` : ''}
            ${data.transactionId ? `<p><strong>ID de transacción:</strong> ${data.transactionId}</p>` : ''}
            <p><strong>Fecha:</strong> ${new Date().toLocaleDateString('es-ES')}</p>
          </div>
          
          <p>Ya puedes acceder a todas las funciones de tu plan. Si configuraste tu contraseña, puedes iniciar sesión directamente. Si no, revisa tu email de bienvenida con las instrucciones.</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/app" style="display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
              Acceder a OposiAI
            </a>
          </div>
        </div>
        
        <div class="footer">
          <p>Guarda este email como comprobante de pago.</p>
          <p>¿Problemas? Contacta nuestro soporte: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
      </body>
      </html>
    `;
    
    const text = `
Pago Confirmado - ${data.customerName}

Tu ${data.planName} ha sido activado correctamente.

Detalles del pago:
- Plan: ${data.planName}
${data.amount ? `- Importe: ${(data.amount / 100).toFixed(2)}€` : ''}
${data.transactionId ? `- ID de transacción: ${data.transactionId}` : ''}
- Fecha: ${new Date().toLocaleDateString('es-ES')}

Ya puedes acceder a OposiAI en: ${process.env.NEXT_PUBLIC_APP_URL}/app

¿Problemas? Contacta: <EMAIL>

El equipo de OposiAI
    `;
    
    return { subject, html, text };
  }
  
  /**
   * Email de notificación al administrador
   */
  static getAdminNotificationEmail(data: EmailTemplateData): {
    subject: string;
    html: string;
    text: string;
  } {
    const subject = `🔔 Nueva suscripción: ${data.planName} - ${data.customerName}`;
    
    const html = `
      <h2>Nueva Suscripción en OposiAI</h2>
      <p><strong>Cliente:</strong> ${data.customerName} (${data.customerEmail})</p>
      <p><strong>Plan:</strong> ${data.planName}</p>
      ${data.amount ? `<p><strong>Importe:</strong> ${(data.amount / 100).toFixed(2)}€</p>` : ''}
      ${data.transactionId ? `<p><strong>Transacción:</strong> ${data.transactionId}</p>` : ''}
      <p><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</p>
      
      <p>✅ Usuario creado automáticamente</p>
      <p>✅ Invitación enviada</p>
      <p>✅ Plan activado</p>
    `;
    
    const text = `
Nueva Suscripción en OposiAI

Cliente: ${data.customerName} (${data.customerEmail})
Plan: ${data.planName}
${data.amount ? `Importe: ${(data.amount / 100).toFixed(2)}€` : ''}
${data.transactionId ? `Transacción: ${data.transactionId}` : ''}
Fecha: ${new Date().toLocaleString('es-ES')}

✅ Usuario creado automáticamente
✅ Invitación enviada  
✅ Plan activado
    `;
    
    return { subject, html, text };
  }
}

import { getFeatureDisplayName } from '@/config/features';
