/**
 * Hook personalizado para manejar la lógica del calendario del plan de estudios
 */

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { PlanEstudiosEstructurado } from '@/features/planificacion/services/planGeneratorService';
import { ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';
import {
  EstadoCalendario,
  DatosPlanCalendario,
  TareaDelDia,
  EstadoDiaCalendario,
  UsePlanCalendarioReturn,
  DiaCalendario
} from '@/features/planificacion/types/calendarTypes';
import {
  procesarPlanParaCalendario,
  obtenerTareasDelDia,
  obtenerEstadoDia
} from '@/features/planificacion/utils/planDateUtils';
import {
  generarFechasCalendario,
  getNombreMes,
  mesAnterior,
  mesSiguiente,
  estaEnMesActual,
  esHoy,
  formatDate,
  setFirstDayOfWeek
} from '@/lib/utils/dateUtils';
import {
  useCalendarioPreferences,
  migrarPreferenciasAntiguas,
  obtenerFechaSeleccionadaGuardada,
  guardarFechaSeleccionada
} from '@/features/planificacion/utils/calendarioPreferences';

/**
 * Hook principal para el manejo del calendario del plan de estudios
 */
export function usePlanCalendario(
  plan: PlanEstudiosEstructurado | null,
  progresoPlan: ProgresoPlanEstudios[],
  fechaInicialSeleccionada?: Date | null
): UsePlanCalendarioReturn {

  // Preferencias del calendario
  const { preferences, updatePreference } = useCalendarioPreferences();

  // Estado del calendario con valores desde localStorage
  const [estadoCalendario, setEstadoCalendario] = useState<EstadoCalendario>(() => {
    // Migrar preferencias antiguas si existen
    migrarPreferenciasAntiguas();

    // Configurar primer día de la semana
    if (preferences.primerDiaSemana !== undefined) {
      setFirstDayOfWeek(preferences.primerDiaSemana);
    }

    const hoy = new Date();
    const fechaGuardada = obtenerFechaSeleccionadaGuardada();

    return {
      yearActual: preferences.yearActual ?? hoy.getFullYear(),
      mesActual: preferences.mesActual ?? hoy.getMonth(),
      fechaSeleccionada: fechaInicialSeleccionada || fechaGuardada || null,
      fechasCalendario: [],
      diasCalendario: []
    };
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);



  // Procesar el plan para obtener datos del calendario
  const datosPlan = useMemo<DatosPlanCalendario | null>(() => {
    if (!plan) return null;

    try {
      setIsLoading(true);
      setError(null);

      const resultado = procesarPlanParaCalendario(plan, progresoPlan, {
        incluirDiasSinTareas: true,
        calcularEstadisticas: true,
        validarFechas: true,
        ordenarTareasPorTipo: true
      });

      if (resultado.errores.length > 0) {
        console.warn('Errores al procesar el plan:', resultado.errores);
        setError(resultado.errores[0]); // Mostrar el primer error
      }

      return resultado.datosPlan;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Error desconocido al procesar el plan';
      setError(errorMsg);
      console.error('Error al procesar plan para calendario:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [plan, progresoPlan]);

  // Generar fechas del calendario para el mes actual
  const fechasCalendario = useMemo(() => {
    return generarFechasCalendario(estadoCalendario.yearActual, estadoCalendario.mesActual);
  }, [estadoCalendario.yearActual, estadoCalendario.mesActual]);

  // Generar días del calendario con información del plan
  const diasCalendario = useMemo<DiaCalendario[]>(() => {
    if (!datosPlan) return [];

    return fechasCalendario.map(fecha => {
      const fechaKey = formatDate(fecha);
      const diaDelPlan = datosPlan.mapaDias.get(fechaKey);
      
      if (diaDelPlan) {
        // Día que existe en el plan
        return {
          ...diaDelPlan,
          estaEnMesActual: estaEnMesActual(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),
          esHoy: esHoy(fecha)
        };
      } else {
        // Día que no está en el plan
        return {
          fecha,
          dia: fecha.getDate(),
          estaEnMesActual: estaEnMesActual(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual),
          esHoy: esHoy(fecha),
          estado: esHoy(fecha) ? 'hoy' : 
                  estaEnMesActual(fecha, estadoCalendario.yearActual, estadoCalendario.mesActual) ? 'normal' : 'fuera-mes',
          tareas: [],
          totalTareas: 0,
          tareasCompletadas: 0,
          porcentajeCompletado: 0
        };
      }
    });
  }, [fechasCalendario, datosPlan, estadoCalendario.yearActual, estadoCalendario.mesActual]);

  // Actualizar estado del calendario cuando cambien las fechas o días
  useEffect(() => {
    setEstadoCalendario(prev => ({
      ...prev,
      fechasCalendario,
      diasCalendario
    }));
  }, [fechasCalendario, diasCalendario]);

  // Navegación del calendario con persistencia
  const navegarMes = useCallback((direccion: 'anterior' | 'siguiente') => {
    setEstadoCalendario(prev => {
      const { year, month } = direccion === 'anterior'
        ? mesAnterior(prev.yearActual, prev.mesActual)
        : mesSiguiente(prev.yearActual, prev.mesActual);

      // Guardar en localStorage
      updatePreference('yearActual', year);
      updatePreference('mesActual', month);

      return {
        ...prev,
        yearActual: year,
        mesActual: month
      };
    });
  }, [updatePreference]);

  const irAMes = useCallback((year: number, month: number) => {
    // Guardar en localStorage
    updatePreference('yearActual', year);
    updatePreference('mesActual', month);

    setEstadoCalendario(prev => ({
      ...prev,
      yearActual: year,
      mesActual: month
    }));
  }, [updatePreference]);

  const seleccionarFecha = useCallback((fecha: Date) => {
    // Guardar fecha seleccionada en localStorage
    guardarFechaSeleccionada(fecha);

    setEstadoCalendario(prev => ({
      ...prev,
      fechaSeleccionada: fecha
    }));
  }, []);

  const irAHoy = useCallback(() => {
    const hoy = new Date();

    // Guardar en localStorage
    updatePreference('yearActual', hoy.getFullYear());
    updatePreference('mesActual', hoy.getMonth());
    guardarFechaSeleccionada(hoy);

    setEstadoCalendario(prev => ({
      ...prev,
      yearActual: hoy.getFullYear(),
      mesActual: hoy.getMonth(),
      fechaSeleccionada: hoy
    }));
  }, [updatePreference]);

  // Utilidades
  const obtenerTareasDelDiaCallback = useCallback((fecha: Date): TareaDelDia[] => {
    if (!datosPlan) return [];
    return obtenerTareasDelDia(fecha, datosPlan);
  }, [datosPlan]);

  const obtenerEstadoDiaCallback = useCallback((fecha: Date): EstadoDiaCalendario => {
    if (!datosPlan) return 'normal';
    return obtenerEstadoDia(fecha, datosPlan);
  }, [datosPlan]);

  const esFechaSeleccionable = useCallback((fecha: Date): boolean => {
    if (!datosPlan) return false;
    
    // Verificar si la fecha está dentro del rango del plan
    const fechaKey = formatDate(fecha);
    const diaDelPlan = datosPlan.mapaDias.get(fechaKey);
    
    // Permitir seleccionar días que tienen tareas o están dentro del rango del plan
    return diaDelPlan !== undefined || 
           (fecha >= datosPlan.fechaInicio && fecha <= datosPlan.fechaFin);
  }, [datosPlan]);

  // Datos computados
  const tituloMes = useMemo(() => {
    const fecha = new Date(estadoCalendario.yearActual, estadoCalendario.mesActual);
    return `${getNombreMes(fecha)} ${estadoCalendario.yearActual}`;
  }, [estadoCalendario.yearActual, estadoCalendario.mesActual]);

  const tareasDelDiaSeleccionado = useMemo(() => {
    if (!estadoCalendario.fechaSeleccionada || !datosPlan) return [];
    return obtenerTareasDelDia(estadoCalendario.fechaSeleccionada, datosPlan);
  }, [estadoCalendario.fechaSeleccionada, datosPlan]);

  const estadisticasDelDia = useMemo(() => {
    if (!estadoCalendario.fechaSeleccionada || !datosPlan) return null;
    
    const fechaKey = formatDate(estadoCalendario.fechaSeleccionada);
    const diaCalendario = datosPlan.mapaDias.get(fechaKey);
    
    if (!diaCalendario) return null;
    
    return {
      total: diaCalendario.totalTareas,
      completadas: diaCalendario.tareasCompletadas,
      porcentaje: diaCalendario.porcentajeCompletado
    };
  }, [estadoCalendario.fechaSeleccionada, datosPlan]);

  return {
    // Estado
    estadoCalendario,
    datosPlan,
    isLoading,
    error,
    
    // Acciones
    navegarMes,
    irAMes,
    seleccionarFecha,
    irAHoy,
    
    // Utilidades
    obtenerTareasDelDia: obtenerTareasDelDiaCallback,
    obtenerEstadoDia: obtenerEstadoDiaCallback,
    esFechaSeleccionable,
    
    // Datos computados
    tituloMes,
    tareasDelDiaSeleccionado,
    estadisticasDelDia
  };
}
