// Script para copiar el código fuente relevante en un solo archivo txt
const fs = require('fs');
const path = require('path');

// Carpetas principales de código
const CODE_DIRS = ['src', 'components', 'hooks', 'lib', 'utils'];
// Extensiones de archivos a incluir
const CODE_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.css', '.json', '.md', '.ts', '.tsx'];
// Carpetas a excluir
const EXCLUDE_DIRS = ['node_modules', 'coverage', '.next', '.git', 'public', 'test', '__mocks__'];

const OUTPUT_FILE = 'codigo_completo_proyecto.txt';

function shouldIncludeFile(filePath) {
  const ext = path.extname(filePath);
  return CODE_EXTENSIONS.includes(ext);
}

function shouldExcludeDir(dirName) {
  return EXCLUDE_DIRS.includes(dirName);
}

function walkDir(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    if (stat.isDirectory()) {
      if (!shouldExcludeDir(file)) {
        walkDir(fullPath, fileList);
      }
    } else if (shouldIncludeFile(fullPath)) {
      fileList.push(fullPath);
    }
  }
  return fileList;
}

function main() {
  let allFiles = [];
  for (const dir of CODE_DIRS) {
    const absDir = path.join(__dirname, dir);
    if (fs.existsSync(absDir)) {
      allFiles = allFiles.concat(walkDir(absDir));
    }
  }
  let output = '';
  for (const file of allFiles) {
    output += `\n\n// ===== Archivo: ${file.replace(__dirname + path.sep, '')} =====\n`;
    output += fs.readFileSync(file, 'utf8');
  }
  fs.writeFileSync(path.join(__dirname, OUTPUT_FILE), output, 'utf8');
  console.log(`Código copiado en ${OUTPUT_FILE}`);
}

main();
