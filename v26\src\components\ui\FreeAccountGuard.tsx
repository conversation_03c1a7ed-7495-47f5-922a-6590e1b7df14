// src/components/ui/FreeAccountGuard.tsx
// Componente para validar límites de cuenta gratuita antes de ejecutar acciones

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { FiAlertTriangle, FiArrowUp, FiX } from 'react-icons/fi';
import { useFreeAccountValidation } from '@/hooks/useFreeAccount';

interface FreeAccountGuardProps {
  feature: 'documents' | 'tests' | 'flashcards' | 'mindMaps';
  amount?: number;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onLimitReached?: () => void;
  showUpgradePrompt?: boolean;
}

export default function FreeAccountGuard({
  feature,
  amount = 1,
  children,
  fallback,
  onLimitReached,
  showUpgradePrompt = true
}: FreeAccountGuardProps) {
  const { isFreeAccount, status, canPerformAction } = useFreeAccountValidation();
  const [showLimitModal, setShowLimitModal] = useState(false);

  // Si no es cuenta gratuita, mostrar contenido normal
  if (!isFreeAccount) {
    return <>{children}</>;
  }

  // Si no hay estado disponible, mostrar fallback o children
  if (!status) {
    return <>{fallback || children}</>;
  }

  // Si la cuenta está expirada, mostrar mensaje de expiración
  if (!status.isActive) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center space-x-3">
          <FiAlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-medium text-red-800">Cuenta Expirada</h3>
            <p className="text-sm text-red-600 mt-1">
              Tu período gratuito ha terminado. Actualiza tu plan para continuar.
            </p>
          </div>
          {showUpgradePrompt && (
            <Link
              href="/upgrade-plan"
              className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
            >
              Actualizar Plan
            </Link>
          )}
        </div>
      </div>
    );
  }

  // Verificar si se puede realizar la acción
  const canPerform = canPerformAction(feature, amount);

  // Si se puede realizar la acción, mostrar contenido normal
  if (canPerform) {
    return <>{children}</>;
  }

  // Si no se puede realizar la acción, mostrar límite alcanzado
  const currentUsage = status.usage[feature] || 0;
  const limit = status.limits[feature] || 0;

  const featureNames = {
    documents: 'documentos',
    tests: 'preguntas de test',
    flashcards: 'flashcards',
    mindMaps: 'mapas mentales'
  };

  const handleLimitReached = () => {
    setShowLimitModal(true);
    onLimitReached?.();
  };

  return (
    <>
      {/* Contenido bloqueado */}
      <div className="relative">
        {/* Overlay */}
        <div className="absolute inset-0 bg-gray-50 bg-opacity-90 rounded-lg z-10 flex items-center justify-center">
          <div className="text-center p-6">
            <FiAlertTriangle className="w-12 h-12 text-orange-500 mx-auto mb-4" />
            <h3 className="font-semibold text-gray-900 mb-2">
              Límite Alcanzado
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Has alcanzado el límite de {featureNames[feature]} ({currentUsage}/{limit})
            </p>
            <button
              onClick={handleLimitReached}
              className="bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-orange-700 transition-colors"
            >
              Ver Detalles
            </button>
          </div>
        </div>

        {/* Contenido original (difuminado) */}
        <div className="filter blur-sm pointer-events-none">
          {fallback || children}
        </div>
      </div>

      {/* Modal de límite alcanzado */}
      {showLimitModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Límite de {featureNames[feature]} alcanzado
              </h3>
              <button
                onClick={() => setShowLimitModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-6">
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                <div className="flex items-center space-x-3">
                  <FiAlertTriangle className="w-5 h-5 text-orange-500" />
                  <div>
                    <p className="font-medium text-orange-800">
                      Has usado {currentUsage} de {limit} {featureNames[feature]}
                    </p>
                    <p className="text-sm text-orange-600 mt-1">
                      Este es el límite para cuentas gratuitas
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">¿Qué puedes hacer?</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start space-x-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span>Actualizar a un plan premium para obtener acceso ilimitado</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span>Esperar a que se renueve tu período gratuito</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span>Explorar otras funcionalidades disponibles</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowLimitModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Entendido
              </button>
              {showUpgradePrompt && (
                <Link
                  href="/upgrade-plan"
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors text-center inline-flex items-center justify-center"
                >
                  <FiArrowUp className="w-4 h-4 mr-2" />
                  Actualizar Plan
                </Link>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Hook para usar el guard programáticamente
export function useFreeAccountGuard() {
  const { validateAndExecute } = useFreeAccountValidation();

  const executeWithGuard = async (
    feature: 'documents' | 'tests' | 'flashcards' | 'mindMaps',
    action: () => Promise<any>,
    amount: number = 1
  ): Promise<{ success: boolean; result?: any; error?: string }> => {
    return validateAndExecute(feature, amount, action);
  };

  return { executeWithGuard };
}
