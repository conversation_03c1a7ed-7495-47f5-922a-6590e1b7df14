/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/profile/route";
exports.ids = ["app/api/user/profile/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v26_src_app_api_user_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user/profile/route.ts */ \"(rsc)/./src/app/api/user/profile/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/profile/route\",\n        pathname: \"/api/user/profile\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/profile/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v26\\\\src\\\\app\\\\api\\\\user\\\\profile\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v26_src_app_api_user_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user/profile/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/user/profile/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _lib_services_planValidation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/planValidation */ \"(rsc)/./src/lib/services/planValidation.ts\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/app/api/user/profile/route.ts\n// API para gestión de perfiles de usuario\n\n\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll () {}\n            }\n        });\n        // Verificar autenticación\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        // Obtener información completa del usuario\n        const accessInfo = await _lib_services_planValidation__WEBPACK_IMPORTED_MODULE_2__.PlanValidationService.getUserAccessInfo(user.id);\n        console.log('[API /user/profile] accessInfo recuperado:', JSON.stringify(accessInfo, null, 2));\n        // Consulta directa para comparar\n        const { data: profileFromDB, error: profileErrorDB } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from('user_profiles').select('*').eq('user_id', user.id).single();\n        console.log('[API /user/profile] Perfil directo de BD:', JSON.stringify(profileFromDB, null, 2));\n        if (profileErrorDB) console.error('[API /user/profile] Error perfil directo BD:', profileErrorDB);\n        if (!accessInfo) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Perfil no encontrado'\n            }, {\n                status: 404\n            });\n        }\n        // Obtener perfil completo de la base de datos\n        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('user_id', user.id).single();\n        if (profileError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error obteniendo perfil'\n            }, {\n                status: 500\n            });\n        }\n        // Verificar si necesita upgrade\n        const upgradeCheck = await _lib_services_planValidation__WEBPACK_IMPORTED_MODULE_2__.PlanValidationService.checkUpgradeNeeded(user.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            user: {\n                id: user.id,\n                email: user.email,\n                name: user.user_metadata?.name || user.email?.split('@')[0],\n                created_at: user.created_at\n            },\n            profile: {\n                ...profile,\n                plan_name: profile.subscription_plan\n            },\n            access: {\n                plan: accessInfo.plan || 'free',\n                features: Array.isArray(accessInfo.features) ? accessInfo.features : [],\n                limits: accessInfo.limits || {},\n                currentUsage: accessInfo.currentUsage || {},\n                paymentVerified: accessInfo.paymentVerified || false\n            },\n            upgrade: upgradeCheck || {\n                needsUpgrade: false\n            },\n            tokenUsage: {\n                current: profile.current_month_tokens || 0,\n                limit: profile.monthly_token_limit || 0,\n                percentage: profile.monthly_token_limit > 0 ? Math.round((profile.current_month_tokens || 0) / profile.monthly_token_limit * 100) : 0,\n                remaining: Math.max(0, (profile.monthly_token_limit || 0) - (profile.current_month_tokens || 0))\n            }\n        });\n    } catch (error) {\n        console.error('Error in profile API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll () {}\n            }\n        });\n        // Verificar autenticación\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No autorizado'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, preferences } = body;\n        // Actualizar metadata del usuario en Auth usando admin client\n        if (name) {\n            const { error: updateError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.auth.admin.updateUserById(user.id, {\n                user_metadata: {\n                    name\n                }\n            });\n            if (updateError) {\n                console.error('Error updating user metadata:', updateError);\n            }\n        }\n        // Actualizar preferencias en el perfil\n        if (preferences) {\n            const { error: profileError } = await supabase.from('user_profiles').update({\n                security_flags: {\n                    ...preferences,\n                    updated_at: new Date().toISOString()\n                },\n                updated_at: new Date().toISOString()\n            }).eq('user_id', user.id);\n            if (profileError) {\n                console.error('Error updating profile preferences:', profileError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error actualizando preferencias'\n                }, {\n                    status: 500\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Perfil actualizado correctamente'\n        });\n    } catch (error) {\n        console.error('Error updating profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user/profile/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/constants.ts":
/*!*********************************!*\
  !*** ./src/config/constants.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* binding */ AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* binding */ AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_LIMITS: () => (/* binding */ FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* binding */ FREE_PLAN_LIMITS),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* binding */ NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* binding */ PAYMENT_STATES),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* binding */ PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* binding */ PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* binding */ RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* binding */ REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* binding */ RETRY_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* binding */ SECURITY_RISK_SCORES),\n/* harmony export */   SEVERITY_LEVELS: () => (/* binding */ SEVERITY_LEVELS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* binding */ TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* binding */ TIMEOUTS),\n/* harmony export */   TOKEN_LIMITS: () => (/* binding */ TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* binding */ VALIDATION_MESSAGES)\n/* harmony export */ });\n// src/config/constants.ts\n// Constantes centralizadas del sistema\n// ============================================================================\n// CONSTANTES DE APLICACIÓN\n// ============================================================================\n/**\n * URLs y rutas de la aplicación\n */ const APP_URLS = {\n    BASE: \"http://localhost:3000\" || 0,\n    SITE: \"http://localhost:3000\" || 0,\n    UPGRADE_PLAN: '/upgrade-plan',\n    THANK_YOU: '/thank-you',\n    LOGIN: '/login',\n    DASHBOARD: '/app',\n    PROFILE: '/profile',\n    WELCOME: '/welcome'\n};\n/**\n * Rutas públicas (no requieren autenticación)\n */ const PUBLIC_ROUTES = [\n    '/',\n    '/login',\n    '/payment',\n    '/thank-you',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/auth/callback',\n    '/auth/confirmed',\n    '/auth/unauthorized',\n    '/auth/reset-password',\n    '/auth/confirm-reset',\n    '/api/auth/register-free',\n    '/api/auth/pre-register-paid',\n    '/api/stripe/webhook',\n    '/api/stripe/create-checkout-session',\n    '/api/stripe/create-token-checkout',\n    '/api/notify-signup',\n    '/api/user/status',\n    '/api/health',\n    '/api/auth/initiate-password-setup'\n];\n/**\n * Rutas que requieren autenticación básica\n */ const AUTHENTICATED_ROUTES = [\n    '/app',\n    '/dashboard',\n    '/profile',\n    '/welcome',\n    '/upgrade-plan'\n];\n// ============================================================================\n// CONSTANTES DE VALIDACIÓN Y LÍMITES\n// ============================================================================\n/**\n * Límites de archivos y uploads\n */ const FILE_LIMITS = {\n    MAX_SIZE_MB: 5,\n    MAX_SIZE_BYTES: 5 * 1024 * 1024,\n    ALLOWED_TYPES: [\n        'application/pdf'\n    ],\n    ALLOWED_EXTENSIONS: [\n        '.pdf'\n    ]\n};\n/**\n * Límites de texto y contenido\n */ const TEXT_LIMITS = {\n    MIN_PASSWORD_LENGTH: 8,\n    MAX_PASSWORD_LENGTH: 128,\n    MAX_DOCUMENT_TITLE_LENGTH: 200,\n    MAX_DESCRIPTION_LENGTH: 500,\n    MIN_CONTENT_LENGTH: 100,\n    MAX_CONTENT_LENGTH: 50000\n};\n/**\n * Límites de tokens y uso\n */ const TOKEN_LIMITS = {\n    DEFAULT_FREE_LIMIT: 50000,\n    WARNING_THRESHOLD_PERCENTAGE: 80,\n    CRITICAL_THRESHOLD_PERCENTAGE: 90,\n    EXCEEDED_THRESHOLD_PERCENTAGE: 100\n};\n/**\n * Límites de rate limiting\n */ const RATE_LIMITS = {\n    DEFAULT_WINDOW_MINUTES: 60,\n    DEFAULT_MAX_REQUESTS: 100,\n    API_REQUESTS_PER_MINUTE: 60,\n    UPLOAD_REQUESTS_PER_HOUR: 10,\n    AUTH_ATTEMPTS_PER_HOUR: 5\n};\n// ============================================================================\n// CONSTANTES DE TIEMPO Y CONFIGURACIÓN\n// ============================================================================\n/**\n * Timeouts y intervalos\n */ const TIMEOUTS = {\n    SESSION_TIMEOUT_MS: 5 * 60 * 1000,\n    API_TIMEOUT_MS: 30 * 1000,\n    UPLOAD_TIMEOUT_MS: 60 * 1000,\n    RETRY_DELAY_MS: 1000,\n    POLLING_INTERVAL_MS: 2000 // 2 segundos\n};\n/**\n * Configuración de reintentos\n */ const RETRY_CONFIG = {\n    MAX_ATTEMPTS: 3,\n    BACKOFF_MULTIPLIER: 2,\n    INITIAL_DELAY_MS: 1000\n};\n/**\n * Configuración de seguridad\n */ const SECURITY_CONFIG = {\n    ENABLE_STRICT_VALIDATION: process.env.STRICT_PLAN_VALIDATION === 'true',\n    REQUIRE_PAYMENT_VERIFICATION: process.env.REQUIRE_PAYMENT_VERIFICATION === 'true',\n    ENABLE_ACCESS_LOGGING: process.env.ENABLE_ACCESS_LOGGING === 'true',\n    ENABLE_FEATURE_VALIDATION: process.env.ENABLE_FEATURE_VALIDATION === 'true',\n    AUTO_ACTIVATE_PAYMENTS: process.env.AUTO_ACTIVATE_PAYMENTS === 'true',\n    ENABLE_PUBLIC_SIGNUP: process.env.ENABLE_PUBLIC_SIGNUP === 'true'\n};\n// ============================================================================\n// CONSTANTES DE MENSAJES Y TEXTOS\n// ============================================================================\n/**\n * Mensajes de error comunes\n */ const ERROR_MESSAGES = {\n    UNAUTHORIZED: 'No autorizado',\n    FORBIDDEN: 'Acceso denegado',\n    NOT_FOUND: 'Recurso no encontrado',\n    INTERNAL_ERROR: 'Error interno del servidor',\n    INVALID_DATA: 'Datos inválidos',\n    USER_NOT_FOUND: 'Usuario no encontrado',\n    PROFILE_NOT_FOUND: 'Perfil de usuario no encontrado',\n    PAYMENT_REQUIRED: 'Pago requerido',\n    LIMIT_EXCEEDED: 'Límite excedido',\n    FILE_TOO_LARGE: 'El archivo es demasiado grande',\n    INVALID_FILE_TYPE: 'Tipo de archivo no válido',\n    UPLOAD_FAILED: 'Error al subir el archivo',\n    PROCESSING_ERROR: 'Error al procesar la solicitud',\n    NETWORK_ERROR: 'Error de conexión',\n    TIMEOUT_ERROR: 'Tiempo de espera agotado'\n};\n/**\n * Mensajes de éxito\n */ const SUCCESS_MESSAGES = {\n    UPLOAD_SUCCESS: 'Archivo subido correctamente',\n    SAVE_SUCCESS: 'Guardado correctamente',\n    UPDATE_SUCCESS: 'Actualizado correctamente',\n    DELETE_SUCCESS: 'Eliminado correctamente',\n    PAYMENT_SUCCESS: 'Pago procesado correctamente',\n    REGISTRATION_SUCCESS: 'Registro completado',\n    LOGIN_SUCCESS: 'Sesión iniciada',\n    LOGOUT_SUCCESS: 'Sesión cerrada',\n    PASSWORD_RESET_SUCCESS: 'Contraseña restablecida',\n    EMAIL_SENT: 'Email enviado correctamente'\n};\n/**\n * Mensajes de validación\n */ const VALIDATION_MESSAGES = {\n    REQUIRED_FIELD: 'Este campo es obligatorio',\n    INVALID_EMAIL: 'Email no válido',\n    PASSWORD_TOO_SHORT: `La contraseña debe tener al menos ${TEXT_LIMITS.MIN_PASSWORD_LENGTH} caracteres`,\n    PASSWORD_TOO_LONG: `La contraseña no puede tener más de ${TEXT_LIMITS.MAX_PASSWORD_LENGTH} caracteres`,\n    PASSWORDS_DONT_MATCH: 'Las contraseñas no coinciden',\n    INVALID_FILE_SIZE: `El archivo no puede superar ${FILE_LIMITS.MAX_SIZE_MB}MB`,\n    INVALID_FILE_FORMAT: 'Formato de archivo no válido',\n    TEXT_TOO_SHORT: `El texto debe tener al menos ${TEXT_LIMITS.MIN_CONTENT_LENGTH} caracteres`,\n    TEXT_TOO_LONG: `El texto no puede superar ${TEXT_LIMITS.MAX_CONTENT_LENGTH} caracteres`\n};\n// ============================================================================\n// CONSTANTES DE ESTADO Y TIPOS\n// ============================================================================\n/**\n * Estados de procesamiento\n */ const PROCESSING_STATES = {\n    IDLE: 'idle',\n    LOADING: 'loading',\n    PROCESSING: 'processing',\n    SUCCESS: 'success',\n    ERROR: 'error',\n    CANCELLED: 'cancelled'\n};\n/**\n * Estados de pago\n */ const PAYMENT_STATES = {\n    PENDING: 'pending',\n    PROCESSING: 'processing',\n    COMPLETED: 'completed',\n    FAILED: 'failed',\n    CANCELLED: 'cancelled',\n    REFUNDED: 'refunded'\n};\n/**\n * Tipos de notificación\n */ const NOTIFICATION_TYPES = {\n    INFO: 'info',\n    SUCCESS: 'success',\n    WARNING: 'warning',\n    ERROR: 'error'\n};\n/**\n * Niveles de severidad\n */ const SEVERITY_LEVELS = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n};\n// ============================================================================\n// CONSTANTES DE CONFIGURACIÓN DE ENTORNO\n// ============================================================================\n/**\n * Variables de entorno requeridas\n */ const REQUIRED_ENV_VARS = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'SUPABASE_SERVICE_ROLE_KEY',\n    'STRIPE_SECRET_KEY',\n    'STRIPE_WEBHOOK_SECRET',\n    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'\n];\n/**\n * Configuración de automatización\n */ const AUTOMATION_CONFIG = {\n    INVITATION_EXPIRY_HOURS: parseInt(process.env.INVITATION_EXPIRY_HOURS || '24'),\n    DEFAULT_TRIAL_DAYS: 5,\n    DEFAULT_GRACE_PERIOD_DAYS: 3\n};\n// ============================================================================\n// CONSTANTES DE BUSINESS LOGIC\n// ============================================================================\n/**\n * Costos y precios (en centavos)\n */ const PRICING = {\n    ADDITIONAL_TOKENS_PRICE: 1000,\n    ADDITIONAL_TOKENS_AMOUNT: 1000000,\n    FREE_PLAN_PRICE: 0,\n    BASIC_PLAN_PRICE: 999,\n    PRO_PLAN_PRICE: 1999 // €19.99\n};\n/**\n * Límites de planes gratuitos\n */ const FREE_PLAN_LIMITS = {\n    DOCUMENTS: 1,\n    MIND_MAPS_TRIAL: 2,\n    TESTS_TRIAL: 10,\n    FLASHCARDS_TRIAL: 10,\n    TOKENS_TRIAL: 50000,\n    TRIAL_DAYS: 5\n};\n/**\n * Factores de riesgo de seguridad\n */ const SECURITY_RISK_SCORES = {\n    MISSING_USER_AGENT: 30,\n    BOT_USER_AGENT: 20,\n    EXTERNAL_REFERER: 10,\n    SUSPICIOUS_PATTERN: 25,\n    HIGH_FREQUENCY: 40\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/features.ts":
/*!********************************!*\
  !*** ./src/config/features.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* binding */ ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* binding */ ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* binding */ ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   FEATURES_CONFIG: () => (/* binding */ FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* binding */ FEATURE_IDS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* binding */ PLAN_RESTRICTED_ROUTES),\n/* harmony export */   actionToFeature: () => (/* binding */ actionToFeature),\n/* harmony export */   activityToFeature: () => (/* binding */ activityToFeature),\n/* harmony export */   featureRequiresPayment: () => (/* binding */ featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* binding */ getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* binding */ getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* binding */ getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* binding */ getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* binding */ getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* binding */ getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* binding */ getFeaturesForPlan),\n/* harmony export */   isValidFeatureId: () => (/* binding */ isValidFeatureId)\n/* harmony export */ });\n// src/config/features.ts\n// Configuración centralizada de características y funcionalidades\n// ============================================================================\n// CONSTANTES DE FEATURES\n// ============================================================================\n/**\n * Identificadores únicos de características del sistema\n */ const FEATURE_IDS = {\n    DOCUMENT_UPLOAD: 'document_upload',\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_TUTOR_CHAT: 'ai_tutor_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_A1_A2: 'summary_a1_a2'\n};\n/**\n * Acciones que pueden realizarse en el sistema\n */ const ACTION_TYPES = {\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_CHAT: 'ai_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_GENERATION: 'summary_generation'\n};\n/**\n * Configuración de todas las características del sistema\n */ const FEATURES_CONFIG = {\n    [FEATURE_IDS.DOCUMENT_UPLOAD]: {\n        id: FEATURE_IDS.DOCUMENT_UPLOAD,\n        name: 'document_upload',\n        displayName: 'Subida de documentos',\n        description: 'Permite subir y procesar documentos PDF para estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 0,\n        icon: 'FiUpload',\n        route: '/app'\n    },\n    [FEATURE_IDS.TEST_GENERATION]: {\n        id: FEATURE_IDS.TEST_GENERATION,\n        name: 'test_generation',\n        displayName: 'Generación de tests',\n        description: 'Genera tests automáticos basados en el contenido de estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 5000,\n        icon: 'FiFileText',\n        route: '/app/tests'\n    },\n    [FEATURE_IDS.FLASHCARD_GENERATION]: {\n        id: FEATURE_IDS.FLASHCARD_GENERATION,\n        name: 'flashcard_generation',\n        displayName: 'Generación de flashcards',\n        description: 'Crea flashcards inteligentes para memorización efectiva',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 3000,\n        icon: 'FiLayers',\n        route: '/app/flashcards'\n    },\n    [FEATURE_IDS.MIND_MAP_GENERATION]: {\n        id: FEATURE_IDS.MIND_MAP_GENERATION,\n        name: 'mind_map_generation',\n        displayName: 'Generación de mapas mentales',\n        description: 'Genera mapas mentales visuales para mejor comprensión',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 4000,\n        icon: 'FiGitBranch',\n        route: '/app/mindmaps'\n    },\n    [FEATURE_IDS.AI_TUTOR_CHAT]: {\n        id: FEATURE_IDS.AI_TUTOR_CHAT,\n        name: 'ai_tutor_chat',\n        displayName: 'Chat con preparador IA',\n        description: 'Interactúa con un preparador de oposiciones inteligente',\n        category: 'premium',\n        minimumPlans: [\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 2000,\n        icon: 'FiMessageSquare',\n        route: '/app/ai-tutor'\n    },\n    [FEATURE_IDS.STUDY_PLANNING]: {\n        id: FEATURE_IDS.STUDY_PLANNING,\n        name: 'study_planning',\n        displayName: 'Planificación de estudios',\n        description: 'Crea planes de estudio personalizados y estructurados',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 20000,\n        icon: 'FiCalendar',\n        route: '/plan-estudios'\n    },\n    [FEATURE_IDS.SUMMARY_A1_A2]: {\n        id: FEATURE_IDS.SUMMARY_A1_A2,\n        name: 'summary_a1_a2',\n        displayName: 'Resúmenes A1 y A2',\n        description: 'Genera resúmenes especializados para oposiciones A1 y A2',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 6000,\n        icon: 'FiBook',\n        route: '/app/summaries'\n    }\n};\n// ============================================================================\n// MAPEOS Y UTILIDADES\n// ============================================================================\n/**\n * Mapeo de acciones a características\n */ const ACTION_TO_FEATURE_MAP = {\n    [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,\n    [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,\n    [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,\n    [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,\n    [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,\n    [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2\n};\n/**\n * Mapeo de actividades de tokens a características\n */ const ACTIVITY_TO_FEATURE_MAP = {\n    'test_generation': FEATURE_IDS.TEST_GENERATION,\n    'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,\n    'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,\n    'ai_tutor_chat': FEATURE_IDS.AI_TUTOR_CHAT,\n    'study_planning': FEATURE_IDS.STUDY_PLANNING,\n    'summary_a1_a2': FEATURE_IDS.SUMMARY_A1_A2,\n    'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD\n};\n/**\n * Configuración de rutas restringidas por plan\n */ const PLAN_RESTRICTED_ROUTES = {\n    '/plan-estudios': [\n        'pro'\n    ],\n    '/app/ai-tutor': [\n        'usuario',\n        'pro'\n    ],\n    '/app/summaries': [\n        'pro'\n    ],\n    '/app/advanced-features': [\n        'pro'\n    ]\n};\n// ============================================================================\n// FUNCIONES UTILITARIAS\n// ============================================================================\n/**\n * Obtiene la configuración de una característica\n */ function getFeatureConfig(featureId) {\n    return FEATURES_CONFIG[featureId];\n}\n/**\n * Obtiene el nombre para mostrar de una característica\n */ function getFeatureDisplayName(featureId) {\n    const config = FEATURES_CONFIG[featureId];\n    return config?.displayName || featureId;\n}\n/**\n * Obtiene todas las características de una categoría\n */ function getFeaturesByCategory(category) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.category === category);\n}\n/**\n * Obtiene las características disponibles para un plan\n */ function getFeaturesForPlan(planId) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.minimumPlans.includes(planId));\n}\n/**\n * Verifica si una característica requiere pago\n */ function featureRequiresPayment(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.requiresPayment || false;\n}\n/**\n * Obtiene los tokens requeridos para una característica\n */ function getFeatureTokensRequired(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.tokensRequired || 0;\n}\n/**\n * Convierte una acción a su característica correspondiente\n */ function actionToFeature(action) {\n    return ACTION_TO_FEATURE_MAP[action];\n}\n/**\n * Convierte una actividad a su característica correspondiente\n */ function activityToFeature(activity) {\n    return ACTIVITY_TO_FEATURE_MAP[activity];\n}\n/**\n * Obtiene todas las características como array\n */ function getAllFeatures() {\n    return Object.values(FEATURES_CONFIG);\n}\n/**\n * Obtiene los IDs de todas las características\n */ function getAllFeatureIds() {\n    return Object.keys(FEATURES_CONFIG);\n}\n/**\n * Verifica si un ID de característica es válido\n */ function isValidFeatureId(featureId) {\n    return featureId in FEATURES_CONFIG;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/features.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/index.ts":
/*!*****************************!*\
  !*** ./src/config/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.APP_URLS),\n/* harmony export */   AUTHENTICATED_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTHENTICATED_ROUTES),\n/* harmony export */   AUTOMATION_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.AUTOMATION_CONFIG),\n/* harmony export */   ERROR_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES),\n/* harmony export */   FEATURES_CONFIG: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.FEATURE_IDS),\n/* harmony export */   FILE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS),\n/* harmony export */   FREE_PLAN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.FREE_PLAN_LIMITS),\n/* harmony export */   LIMITS: () => (/* binding */ LIMITS),\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES),\n/* harmony export */   NOTIFICATION_TYPES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_TYPES),\n/* harmony export */   PAYMENT_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PAYMENT_STATES),\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.PLAN_RESTRICTED_ROUTES),\n/* harmony export */   PRICING: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PRICING),\n/* harmony export */   PROCESSING_STATES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PROCESSING_STATES),\n/* harmony export */   PUBLIC_ROUTES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.PUBLIC_ROUTES),\n/* harmony export */   RATE_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS),\n/* harmony export */   REQUIRED_ENV_VARS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.REQUIRED_ENV_VARS),\n/* harmony export */   RETRY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG),\n/* harmony export */   SECURITY: () => (/* binding */ SECURITY),\n/* harmony export */   SECURITY_CONFIG: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG),\n/* harmony export */   SECURITY_RISK_SCORES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES),\n/* harmony export */   TEXT_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS),\n/* harmony export */   TIMEOUTS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS),\n/* harmony export */   TIME_CONFIG: () => (/* binding */ TIME_CONFIG),\n/* harmony export */   TOKEN_LIMITS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS),\n/* harmony export */   VALIDATION_MESSAGES: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES),\n/* harmony export */   actionToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.actionToFeature),\n/* harmony export */   activityToFeature: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.activityToFeature),\n/* harmony export */   canPerformAction: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.checkUserFeatureAccess),\n/* harmony export */   featureRequiresPayment: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.getFeaturesForPlan),\n/* harmony export */   getPlanConfiguration: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_0__.isUnlimited),\n/* harmony export */   isValidFeatureId: () => (/* reexport safe */ _features__WEBPACK_IMPORTED_MODULE_1__.isValidFeatureId)\n/* harmony export */ });\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/config/plans.ts\");\n/* harmony import */ var _features__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./features */ \"(rsc)/./src/config/features.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./src/config/constants.ts\");\n/**\n * Configuración centralizada - Re-exports\n * \n * Este archivo centraliza todas las configuraciones del sistema,\n * proporcionando un punto único de importación para cualquier configuración.\n * \n * Uso:\n * import { PLAN_CONFIGURATIONS, FEATURES_CONFIG, ERROR_MESSAGES } from '@/config';\n */ // ============================================================================\n// CONFIGURACIONES DE PLANES\n// ============================================================================\n\n// ============================================================================\n// CONFIGURACIONES DE FEATURES\n// ============================================================================\n\n// ============================================================================\n// CONSTANTES DEL SISTEMA\n// ============================================================================\n\n// ============================================================================\n// RE-EXPORTS COMBINADOS PARA CONVENIENCIA\n// ============================================================================\n// Importar constantes para uso en re-exports combinados\n\n/**\n * Todas las configuraciones de límites en un solo objeto\n */ const LIMITS = {\n    FILE: _constants__WEBPACK_IMPORTED_MODULE_2__.FILE_LIMITS,\n    TEXT: _constants__WEBPACK_IMPORTED_MODULE_2__.TEXT_LIMITS,\n    TOKEN: _constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_LIMITS,\n    RATE: _constants__WEBPACK_IMPORTED_MODULE_2__.RATE_LIMITS\n};\n/**\n * Todas las configuraciones de tiempo en un solo objeto\n */ const TIME_CONFIG = {\n    TIMEOUTS: _constants__WEBPACK_IMPORTED_MODULE_2__.TIMEOUTS,\n    RETRY: _constants__WEBPACK_IMPORTED_MODULE_2__.RETRY_CONFIG\n};\n/**\n * Todos los mensajes del sistema en un solo objeto\n */ const MESSAGES = {\n    ERROR: _constants__WEBPACK_IMPORTED_MODULE_2__.ERROR_MESSAGES,\n    SUCCESS: _constants__WEBPACK_IMPORTED_MODULE_2__.SUCCESS_MESSAGES,\n    VALIDATION: _constants__WEBPACK_IMPORTED_MODULE_2__.VALIDATION_MESSAGES\n};\n/**\n * Todas las configuraciones de seguridad en un solo objeto\n */ const SECURITY = {\n    CONFIG: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_CONFIG,\n    RISK_SCORES: _constants__WEBPACK_IMPORTED_MODULE_2__.SECURITY_RISK_SCORES\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 500000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 500000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/freeAccountService.ts":
/*!************************************************!*\
  !*** ./src/lib/services/freeAccountService.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FreeAccountService: () => (/* binding */ FreeAccountService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/plans */ \"(rsc)/./src/config/plans.ts\");\n// src/lib/services/freeAccountService.ts\n// Servicio para gestión automatizada de cuentas gratuitas\n\n\nclass FreeAccountService {\n    /**\n   * Crear cuenta gratuita automáticamente\n   * Ahora crea el usuario directamente y genera un enlace de tipo 'recovery' para establecer la contraseña.\n   */ static async createFreeAccount(request) {\n        try {\n            console.log('🆓 Iniciando creación de cuenta gratuita (flujo de invitación):', request.email);\n            // 1. Validar que el email no esté ya registrado\n            try {\n                const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                if (existingUser) {\n                    return {\n                        success: false,\n                        error: 'El email ya está registrado en el sistema.'\n                    };\n                }\n            } catch (error) {\n                if (error instanceof Error && error.message.toLowerCase().includes('user not found')) {\n                    console.log('Usuario no existe en Auth, continuando con la invitación.');\n                } else {\n                    console.warn('Advertencia al verificar usuario existente, se continuará con el intento de invitación:', error);\n                }\n            }\n            // 2. Calcular fecha de expiración (5 días desde ahora)\n            const expiresAt = new Date();\n            expiresAt.setDate(expiresAt.getDate() + 5);\n            // 3. Preparar metadatos para el usuario invitado\n            const userDataForCreation = {\n                name: request.name || request.email.split('@')[0],\n                plan: 'free',\n                free_account: true,\n                expires_at: expiresAt.toISOString(),\n                created_via: 'free_invitation_flow',\n                registration_type: 'automatic_free_invitation',\n                requires_password_setup: true\n            };\n            console.log('📧 Invitando nuevo usuario con datos:', {\n                email: request.email,\n                userData: userDataForCreation,\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirm-invitation`\n            });\n            // 4. Invitar al usuario. Esto crea el usuario y envía el email de invitación.\n            const { data: { user: newUser }, error: inviteError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.inviteUserByEmail(request.email, {\n                data: userDataForCreation,\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirm-invitation`\n            });\n            if (inviteError) {\n                console.error('❌ Error invitando al usuario:', inviteError);\n                if (inviteError.message.includes('User already registered')) {\n                    return {\n                        success: false,\n                        error: 'Ya existe una cuenta con este email.'\n                    };\n                }\n                throw new Error(`Error invitando al usuario: ${inviteError.message}`);\n            }\n            if (!newUser) {\n                throw new Error('Usuario no devuelto después de la invitación.');\n            }\n            console.log('✅ Usuario invitado exitosamente a Supabase Auth:', newUser.id);\n            // 5. Crear perfil de usuario y registrar historial de forma atómica\n            const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            if (!planConfig) {\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup\n                throw new Error('Configuración de plan gratuito no encontrada');\n            }\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const profileDataForRPC = {\n                subscription_plan: 'free',\n                monthly_token_limit: (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)('free'),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                stripe_customer_id: null,\n                stripe_subscription_id: null,\n                last_payment_date: null,\n                auto_renew: false,\n                plan_expires_at: expiresAt.toISOString(),\n                plan_features: planConfig.features,\n                security_flags: {\n                    created_via_free_invitation_flow: true,\n                    free_account: true,\n                    expires_at: expiresAt.toISOString(),\n                    activation_date: new Date().toISOString(),\n                    usage_count: {\n                        documents: 0,\n                        tests: 0,\n                        flashcards: 0,\n                        mindMaps: 0,\n                        tokens: 0\n                    }\n                }\n            };\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: newUser.id,\n                p_transaction_id: null,\n                p_profile_data: profileDataForRPC\n            }).single();\n            if (rpcError) {\n                console.error('❌ Error al ejecutar la función RPC create_user_profile_and_history:', rpcError);\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);\n            console.log('🎉 Cuenta gratuita creada exitosamente con flujo de invitación.');\n            return {\n                success: true,\n                userId: newUser.id,\n                profileId: profileId,\n                expiresAt: expiresAt.toISOString()\n            };\n        } catch (error) {\n            console.error('❌ Error crítico en la creación de cuenta gratuita:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido al crear cuenta gratuita'\n            };\n        }\n    }\n    // Las funciones getFreeAccountStatus, incrementUsageCount, canPerformAction, cleanupExpiredAccounts\n    // permanecen igual que antes, ya que su lógica no depende directamente de cómo se creó el usuario,\n    // sino de los datos en user_profiles.\n    /**\n   * Verificar estado de cuenta gratuita\n   */ static async getFreeAccountStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return null;\n            }\n            const now = new Date();\n            const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;\n            if (!expiresAt) {\n                return null;\n            }\n            const isActive = now < expiresAt;\n            const timeDiff = expiresAt.getTime() - now.getTime();\n            const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n            const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));\n            const usageCount = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0\n            };\n            usageCount.tokens = profile.current_month_tokens || 0;\n            const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            const limits = {\n                documents: planConfig?.limits.documents || 1,\n                tests: planConfig?.limits.testsForTrial || 10,\n                flashcards: planConfig?.limits.flashcardsForTrial || 10,\n                mindMaps: planConfig?.limits.mindMapsForTrial || 2,\n                tokens: planConfig?.limits.tokensForTrial || 50000\n            };\n            // Calcular progressPercentage\n            const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms\n            const creationDate = new Date(profile.created_at || Date.now()); // Usar created_at o now si no está\n            const activationDate = profile.security_flags?.activation_date ? new Date(profile.security_flags.activation_date) : creationDate;\n            const timeElapsed = now.getTime() - activationDate.getTime();\n            const progressPercentage = Math.min(100, Math.max(0, timeElapsed / totalDuration * 100));\n            return {\n                isActive,\n                expiresAt: expiresAt.toISOString(),\n                daysRemaining,\n                hoursRemaining,\n                usageCount,\n                limits,\n                // @ts-ignore Asegurar que progressPercentage está en el tipo si es necesario\n                progressPercentage: Math.round(progressPercentage)\n            };\n        } catch (error) {\n            console.error('Error obteniendo estado de cuenta gratuita:', error);\n            return null;\n        }\n    }\n    /**\n   * Incrementar contador de uso\n   */ static async incrementUsageCount(userId, feature, amount = 1) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return false;\n            }\n            const currentUsage = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0\n            };\n            currentUsage[feature] = (currentUsage[feature] || 0) + amount;\n            const updateData = {\n                security_flags: {\n                    ...profile.security_flags,\n                    usage_count: currentUsage\n                },\n                updated_at: new Date().toISOString()\n            };\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update(updateData).eq('user_id', userId);\n            return true;\n        } catch (error) {\n            console.error('Error incrementando contador de uso:', error);\n            return false;\n        }\n    }\n    /**\n   * Verificar si se puede realizar una acción\n   */ static async canPerformAction(userId, feature, amount = 1) {\n        try {\n            const status = await this.getFreeAccountStatus(userId);\n            if (!status) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta no encontrada o no es gratuita'\n                };\n            }\n            if (!status.isActive) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta gratuita expirada'\n                };\n            }\n            const currentUsage = status.usageCount[feature] || 0;\n            const limit = status.limits[feature];\n            const remaining = limit - currentUsage;\n            if (currentUsage + amount > limit) {\n                return {\n                    allowed: false,\n                    reason: `Límite de ${feature} alcanzado (${currentUsage}/${limit})`,\n                    remaining: Math.max(0, remaining)\n                };\n            }\n            return {\n                allowed: true,\n                remaining: remaining - amount\n            };\n        } catch (error) {\n            console.error('Error verificando acción:', error);\n            return {\n                allowed: false,\n                reason: 'Error interno'\n            };\n        }\n    }\n    /**\n   * Limpiar cuentas gratuitas expiradas\n   */ static async cleanupExpiredAccounts() {\n        try {\n            console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');\n            const now = new Date().toISOString();\n            const { data: expiredProfiles, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, id').eq('subscription_plan', 'free').lt('plan_expires_at', now)// Añadir condición para asegurar que no se desactiven cuentas ya deshabilitadas\n            .neq('security_flags ->> account_disabled', 'true');\n            if (error) {\n                throw new Error(`Error buscando cuentas expiradas: ${error.message}`);\n            }\n            if (!expiredProfiles || expiredProfiles.length === 0) {\n                console.log('✅ No hay cuentas expiradas para limpiar');\n                return {\n                    cleaned: 0,\n                    errors: []\n                };\n            }\n            console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas para procesar`);\n            const errors = [];\n            let cleaned = 0;\n            for (const profile of expiredProfiles){\n                try {\n                    // Desactivar usuario en auth\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.updateUserById(profile.user_id, {\n                        user_metadata: {\n                            account_disabled: true,\n                            disabled_reason: 'free_account_expired'\n                        }\n                    });\n                    // Marcar perfil como inactivo\n                    // Obtener security_flags existentes para no sobrescribirlas\n                    const { data: currentProfileData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('security_flags').eq('user_id', profile.user_id).single();\n                    const existingFlags = currentProfileData?.security_flags || {};\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: false,\n                        security_flags: {\n                            ...existingFlags,\n                            account_disabled: true,\n                            disabled_at: new Date().toISOString(),\n                            disabled_reason: 'free_account_expired'\n                        }\n                    }).eq('user_id', profile.user_id);\n                    cleaned++;\n                } catch (cleanupError) {\n                    const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);\n            return {\n                cleaned,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en limpieza de cuentas:', error);\n            return {\n                cleaned: 0,\n                errors: [\n                    error instanceof Error ? error.message : 'Error desconocido'\n                ]\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/freeAccountService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/planValidation.ts":
/*!********************************************!*\
  !*** ./src/lib/services/planValidation.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlanValidationService: () => (/* binding */ PlanValidationService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config */ \"(rsc)/./src/config/index.ts\");\n/* harmony import */ var _freeAccountService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./freeAccountService */ \"(rsc)/./src/lib/services/freeAccountService.ts\");\n/* harmony import */ var _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/webhookLogger */ \"(rsc)/./src/lib/utils/webhookLogger.ts\");\n// src/lib/services/planValidation.ts\n// Servicio para validación de planes y acceso a características\n\n\n\n\nclass PlanValidationService {\n    /**\n   * Validar acceso de usuario a una característica específica\n   */ static async validateFeatureAccess(userId, featureName, tokensToUse = 0) {\n        try {\n            // Obtener perfil del usuario\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                await _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__.WebhookLogger.logFeatureAccess(userId, featureName, false, 'unknown', 0, 'User profile not found');\n                return {\n                    allowed: false,\n                    reason: _config__WEBPACK_IMPORTED_MODULE_1__.ERROR_MESSAGES.PROFILE_NOT_FOUND\n                };\n            }\n            // Verificar pago para planes de pago\n            if (profile.subscription_plan !== 'free' && !profile.payment_verified) {\n                await _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__.WebhookLogger.logFeatureAccess(userId, featureName, false, profile.subscription_plan, 0, 'Payment not verified');\n                return {\n                    allowed: false,\n                    reason: 'Pago no verificado. Complete el proceso de pago para acceder a esta característica.'\n                };\n            }\n            // Verificar acceso a la característica según el plan\n            const hasAccess = (0,_config__WEBPACK_IMPORTED_MODULE_1__.hasFeatureAccess)(profile.subscription_plan, featureName);\n            if (!hasAccess) {\n                await _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__.WebhookLogger.logFeatureAccess(userId, featureName, false, profile.subscription_plan, 0, `Feature not available in ${profile.subscription_plan} plan`);\n                return {\n                    allowed: false,\n                    reason: `La característica ${featureName} no está disponible en su plan ${profile.subscription_plan}`\n                };\n            }\n            // Verificar límites de tokens si aplica\n            if (tokensToUse > 0) {\n                const tokenValidation = await this.validateTokenUsage(profile, tokensToUse);\n                if (!tokenValidation.allowed) {\n                    await _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__.WebhookLogger.logFeatureAccess(userId, featureName, false, profile.subscription_plan, tokensToUse, tokenValidation.reason);\n                    return tokenValidation;\n                }\n            }\n            // Log de acceso exitoso\n            await _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__.WebhookLogger.logFeatureAccess(userId, featureName, true, profile.subscription_plan, tokensToUse);\n            return {\n                allowed: true,\n                remainingUsage: profile.monthly_token_limit - profile.current_month_tokens,\n                planLimits: {\n                    monthlyTokens: profile.monthly_token_limit,\n                    currentTokens: profile.current_month_tokens\n                }\n            };\n        } catch (error) {\n            console.error('Error validating feature access:', error);\n            await _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_3__.WebhookLogger.logFeatureAccess(userId, featureName, false, 'error', tokensToUse, 'Internal validation error');\n            return {\n                allowed: false,\n                reason: 'Error interno de validación'\n            };\n        }\n    }\n    /**\n   * Validar uso de tokens\n   */ static async validateTokenUsage(profile, tokensToUse) {\n        const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n        // Reset de tokens si es un nuevo mes\n        if (profile.current_month !== currentMonth) {\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.upsertUserProfile({\n                ...profile,\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                updated_at: new Date().toISOString()\n            });\n            profile.current_month_tokens = 0;\n            profile.current_month = currentMonth;\n        }\n        // Verificar si tiene tokens suficientes\n        const tokensAfterUse = profile.current_month_tokens + tokensToUse;\n        if (tokensAfterUse > profile.monthly_token_limit) {\n            return {\n                allowed: false,\n                reason: `Límite mensual de tokens alcanzado. Usado: ${profile.current_month_tokens}/${profile.monthly_token_limit}`,\n                remainingUsage: Math.max(0, profile.monthly_token_limit - profile.current_month_tokens)\n            };\n        }\n        return {\n            allowed: true,\n            remainingUsage: profile.monthly_token_limit - tokensAfterUse\n        };\n    }\n    /**\n   * Obtener información completa de acceso del usuario\n   */ static async getUserAccessInfo(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                return null;\n            }\n            const planConfig = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(profile.subscription_plan);\n            if (!planConfig) {\n                return null;\n            }\n            // Obtener uso actual de características\n            let currentUsage = {\n                tokens: profile.current_month_tokens,\n                tokenLimit: profile.monthly_token_limit,\n                month: profile.current_month,\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0\n            };\n            // Obtener conteo real de documentos para todos los usuarios\n            try {\n                const documentsCount = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getDocumentsCount(userId);\n                currentUsage.documents = documentsCount;\n            } catch (error) {\n                console.error('Error getting documents count:', error);\n            }\n            // Si es plan gratuito, obtener uso actual de FreeAccountService\n            if (profile.subscription_plan === 'free') {\n                try {\n                    const { FreeAccountService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./freeAccountService */ \"(rsc)/./src/lib/services/freeAccountService.ts\"));\n                    const status = await FreeAccountService.getFreeAccountStatus(userId);\n                    if (status) {\n                        currentUsage = {\n                            ...currentUsage,\n                            tests: status.usageCount.tests || 0,\n                            flashcards: status.usageCount.flashcards || 0,\n                            mindMaps: status.usageCount.mindMaps || 0\n                        };\n                    }\n                } catch (error) {\n                    console.error('Error getting free account usage:', error);\n                }\n            }\n            // Mapear límites a las claves esperadas por el frontend\n            const mappedLimits = {\n                // Mantener las claves originales primero\n                ...planConfig.limits,\n                // Luego mapear a las claves esperadas por el frontend\n                tests: planConfig.limits.testsPerWeek ?? 0,\n                flashcards: planConfig.limits.flashcardsPerWeek ?? 0,\n                mindMaps: planConfig.limits.mindMapsPerWeek ?? 0\n            };\n            return {\n                userId,\n                plan: profile.subscription_plan,\n                paymentVerified: profile.payment_verified,\n                features: planConfig.features || [],\n                limits: mappedLimits,\n                currentUsage\n            };\n        } catch (error) {\n            console.error('Error getting user access info:', error);\n            return null;\n        }\n    }\n    /**\n   * Verificar si el usuario puede realizar una acción específica\n   */ static async canUserPerformAction(userId, action, quantity = 1) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                return {\n                    allowed: false,\n                    reason: _config__WEBPACK_IMPORTED_MODULE_1__.ERROR_MESSAGES.USER_NOT_FOUND\n                };\n            }\n            // Mapear acciones a características usando configuración centralizada\n            const featureName = _config__WEBPACK_IMPORTED_MODULE_1__.ACTION_TO_FEATURE_MAP[action];\n            // Verificar acceso básico a la característica\n            const basicValidation = await this.validateFeatureAccess(userId, featureName, quantity);\n            if (!basicValidation.allowed) {\n                return basicValidation;\n            }\n            // Para plan gratuito, verificar límites específicos usando FreeAccountService\n            if (profile.subscription_plan === 'free') {\n                const featureMap = {\n                    'test_generation': 'tests',\n                    'flashcard_generation': 'flashcards',\n                    'mind_map_generation': 'mindMaps'\n                };\n                const featureKey = featureMap[action];\n                if (featureKey) {\n                    const canPerform = await _freeAccountService__WEBPACK_IMPORTED_MODULE_2__.FreeAccountService.canPerformAction(userId, featureKey, quantity);\n                    if (!canPerform.allowed) {\n                        return {\n                            allowed: false,\n                            reason: canPerform.reason || `Límite alcanzado para ${action}`,\n                            remainingUsage: canPerform.remaining\n                        };\n                    }\n                }\n            }\n            return {\n                allowed: true,\n                remainingUsage: basicValidation.remainingUsage\n            };\n        } catch (error) {\n            console.error('Error checking user action:', error);\n            return {\n                allowed: false,\n                reason: 'Error interno de validación'\n            };\n        }\n    }\n    /**\n   * Actualizar uso de tokens después de una operación\n   */ static async updateTokenUsage(userId, tokensUsed, activity) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                return false;\n            }\n            const newTokenCount = profile.current_month_tokens + tokensUsed;\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.upsertUserProfile({\n                ...profile,\n                current_month_tokens: newTokenCount,\n                updated_at: new Date().toISOString()\n            });\n            return true;\n        } catch (error) {\n            console.error('Error updating token usage:', error);\n            return false;\n        }\n    }\n    /**\n   * Verificar si el usuario necesita upgrade de plan\n   */ static async checkUpgradeNeeded(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                return {\n                    needsUpgrade: false\n                };\n            }\n            // Verificar si está cerca del límite de tokens\n            const usagePercentage = profile.current_month_tokens / profile.monthly_token_limit * 100;\n            if (usagePercentage >= _config__WEBPACK_IMPORTED_MODULE_1__.TOKEN_LIMITS.CRITICAL_THRESHOLD_PERCENTAGE) {\n                const suggestedPlan = profile.subscription_plan === 'free' ? 'usuario' : 'pro';\n                return {\n                    needsUpgrade: true,\n                    reason: `Has usado el ${usagePercentage.toFixed(1)}% de tus tokens mensuales`,\n                    suggestedPlan\n                };\n            }\n            return {\n                needsUpgrade: false\n            };\n        } catch (error) {\n            console.error('Error checking upgrade need:', error);\n            return {\n                needsUpgrade: false\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/planValidation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/webhookLogger.ts":
/*!****************************************!*\
  !*** ./src/lib/utils/webhookLogger.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebhookLogger: () => (/* binding */ WebhookLogger)\n/* harmony export */ });\n// src/lib/utils/webhookLogger.ts\n// Sistema de logging y auditoría para webhooks\nclass WebhookLogger {\n    /**\n   * Log de evento de webhook procesado\n   */ static async logWebhookEvent(entry) {\n        try {\n            // Log en consola con formato estructurado\n            const logLevel = entry.success ? '✅' : '❌';\n            const timestamp = new Date().toISOString();\n            console.log(`${logLevel} [WEBHOOK] ${timestamp}`, {\n                eventType: entry.eventType,\n                eventId: entry.eventId,\n                success: entry.success,\n                processingTime: `${entry.processingTime}ms`,\n                message: entry.message,\n                ...entry.error && {\n                    error: entry.error\n                },\n                ...entry.data && {\n                    data: entry.data\n                }\n            });\n            // En producción, aquí podrías enviar a servicios de logging externos\n            // como DataDog, LogRocket, Sentry, etc.\n            if (false) {}\n        } catch (error) {\n            console.error('Error logging webhook event:', error);\n        }\n    }\n    /**\n   * Log de acceso a características\n   */ static async logFeatureAccess(userId, featureName, accessGranted, planAtTime, tokensUsed = 0, denialReason) {\n        try {\n            // Solo intentar log en BD si estamos en el servidor (donde SUPABASE_SERVICE_ROLE_KEY está disponible)\n            if ( true && process.env.SUPABASE_SERVICE_ROLE_KEY) {\n                // Import dinámico para evitar problemas en el cliente\n                const { SupabaseAdminService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\"));\n                await SupabaseAdminService.logFeatureAccess({\n                    user_id: userId,\n                    feature_name: featureName,\n                    access_granted: accessGranted,\n                    plan_at_time: planAtTime,\n                    tokens_used: tokensUsed,\n                    denial_reason: denialReason\n                });\n            }\n            const logLevel = accessGranted ? '✅' : '❌';\n            console.log(`${logLevel} [FEATURE_ACCESS]`, {\n                userId,\n                feature: featureName,\n                granted: accessGranted,\n                plan: planAtTime,\n                tokens: tokensUsed,\n                ...denialReason && {\n                    reason: denialReason\n                }\n            });\n        } catch (error) {\n            console.error('Error logging feature access:', error);\n        }\n    }\n    /**\n   * Log de cambio de plan\n   */ static async logPlanChange(userId, oldPlan, newPlan, changedBy, reason, transactionId) {\n        try {\n            // Solo intentar log en BD si estamos en el servidor (donde SUPABASE_SERVICE_ROLE_KEY está disponible)\n            if ( true && process.env.SUPABASE_SERVICE_ROLE_KEY) {\n                // Import dinámico para evitar problemas en el cliente\n                const { SupabaseAdminService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\"));\n                await SupabaseAdminService.logPlanChange({\n                    user_id: userId,\n                    old_plan: oldPlan || undefined,\n                    new_plan: newPlan,\n                    changed_by: changedBy,\n                    reason,\n                    transaction_id: transactionId\n                });\n            }\n            console.log('🔄 [PLAN_CHANGE]', {\n                userId,\n                oldPlan,\n                newPlan,\n                changedBy,\n                reason,\n                transactionId\n            });\n        } catch (error) {\n            console.error('Error logging plan change:', error);\n        }\n    }\n    /**\n   * Log de error crítico\n   */ static async logCriticalError(context, error, additionalData) {\n        try {\n            const errorLog = {\n                context,\n                message: error.message,\n                stack: error.stack,\n                timestamp: new Date().toISOString(),\n                additionalData\n            };\n            console.error('🚨 [CRITICAL_ERROR]', errorLog);\n            // En producción, enviar alertas inmediatas\n            if (false) {}\n        } catch (logError) {\n            console.error('Error logging critical error:', logError);\n        }\n    }\n    /**\n   * Log de métricas de rendimiento\n   */ static logPerformanceMetrics(operation, duration, success, additionalMetrics) {\n        const metrics = {\n            operation,\n            duration: `${duration}ms`,\n            success,\n            timestamp: new Date().toISOString(),\n            ...additionalMetrics\n        };\n        console.log('📊 [PERFORMANCE]', metrics);\n        // En producción, enviar a servicio de métricas\n        if (false) {}\n    }\n    /**\n   * Enviar logs a servicio externo (placeholder)\n   */ static async logToExternalService(entry) {\n    // Implementar integración con servicios como:\n    // - DataDog: https://docs.datadoghq.com/api/latest/logs/\n    // - LogRocket: https://docs.logrocket.com/reference/api\n    // - Sentry: https://docs.sentry.io/api/\n    // Ejemplo con fetch genérico:\n    /*\n    try {\n      await fetch(process.env.EXTERNAL_LOGGING_ENDPOINT!, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${process.env.LOGGING_API_KEY}`\n        },\n        body: JSON.stringify({\n          service: 'oposiai-webhooks',\n          level: entry.success ? 'info' : 'error',\n          message: entry.message,\n          metadata: entry\n        })\n      });\n    } catch (error) {\n      console.error('Failed to send log to external service:', error);\n    }\n    */ }\n    /**\n   * Enviar alerta crítica (placeholder)\n   */ static async sendCriticalAlert(errorLog) {\n    // Implementar alertas críticas via:\n    // - Slack webhook\n    // - Email\n    // - SMS\n    // - PagerDuty\n    // Ejemplo con Slack:\n    /*\n    try {\n      await fetch(process.env.SLACK_WEBHOOK_URL!, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          text: `🚨 Critical Error in OposiAI`,\n          blocks: [\n            {\n              type: 'section',\n              text: {\n                type: 'mrkdwn',\n                text: `*Context:* ${errorLog.context}\\n*Error:* ${errorLog.message}\\n*Time:* ${errorLog.timestamp}`\n              }\n            }\n          ]\n        })\n      });\n    } catch (error) {\n      console.error('Failed to send critical alert:', error);\n    }\n    */ }\n    /**\n   * Enviar métricas (placeholder)\n   */ static sendMetrics(metrics) {\n        // Implementar envío de métricas a:\n        // - DataDog\n        // - New Relic\n        // - CloudWatch\n        // - Prometheus\n        // Por ahora solo log local\n        if (process.env.ENABLE_METRICS_LOGGING === 'true') {\n            console.log('📈 [METRICS_EXPORT]', metrics);\n        }\n    }\n    /**\n   * Obtener estadísticas de webhooks\n   */ static async getWebhookStats(timeframe = 'day') {\n        // Esta función requeriría una tabla de logs en la BD\n        // Por ahora retornamos estructura básica\n        return {\n            totalEvents: 0,\n            successRate: 0,\n            averageProcessingTime: 0,\n            errorsByType: {}\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/webhookLogger.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv26&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();