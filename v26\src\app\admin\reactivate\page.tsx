'use client';

import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

export default function ReactivateUserPage() {
  const [sessionId, setSessionId] = useState('');
  const [email, setEmail] = useState('');
  const [userId, setUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleReactivate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!sessionId && !email && !userId) {
      toast.error('Proporciona al menos un identificador (Session ID, Email o User ID)');
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/reactivate-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: sessionId || undefined,
          email: email || undefined,
          userId: userId || undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Usuario reactivado exitosamente');
        setResult(data);
        // Limpiar formulario
        setSessionId('');
        setEmail('');
        setUserId('');
      } else {
        toast.error(data.error || 'Error reactivando usuario');
        setResult({ error: data.error });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error de conexión');
      setResult({ error: 'Error de conexión' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Reactivar Usuario
            </h1>
            <p className="text-gray-600 mt-2">
              Herramienta administrativa para reactivar usuarios cuando el webhook de Stripe falla.
            </p>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Uso Administrativo
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>Esta herramienta debe usarse solo cuando:</p>
                  <ul className="list-disc list-inside mt-1">
                    <li>El webhook de Stripe falló</li>
                    <li>El pago está confirmado en Stripe</li>
                    <li>El usuario no recibió su email de activación</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <form onSubmit={handleReactivate} className="space-y-6">
            <div>
              <label htmlFor="sessionId" className="block text-sm font-medium text-gray-700">
                Stripe Session ID
              </label>
              <input
                type="text"
                id="sessionId"
                value={sessionId}
                onChange={(e) => setSessionId(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="cs_test_..."
                disabled={isLoading}
              />
              <p className="mt-1 text-sm text-gray-500">
                ID de la sesión de checkout de Stripe (recomendado)
              </p>
            </div>

            <div className="text-center text-gray-500 text-sm">
              - O -
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email del Usuario
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>

            <div>
              <label htmlFor="userId" className="block text-sm font-medium text-gray-700">
                User ID de Supabase
              </label>
              <input
                type="text"
                id="userId"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="uuid-del-usuario"
                disabled={isLoading}
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Reactivando...' : 'Reactivar Usuario'}
            </button>
          </form>

          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Resultado
              </h3>
              
              {result.success ? (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">
                        Usuario reactivado exitosamente
                      </h3>
                      <div className="mt-2 text-sm text-green-700">
                        <ul className="list-disc list-inside">
                          <li>User ID: {result.data?.userId}</li>
                          {result.data?.transactionId && <li>Transaction ID: {result.data.transactionId}</li>}
                          <li>Email enviado: {result.data?.emailSent ? 'Sí' : 'No'}</li>
                          <li>Perfil actualizado: {result.data?.profileUpdated ? 'Sí' : 'No'}</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Error en la reactivación
                      </h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{result.error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
