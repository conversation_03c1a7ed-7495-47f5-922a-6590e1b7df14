'use client';

import React from 'react';
import { FiX, FiBarChart } from 'react-icons/fi';
import { usePlanLimits } from '@/hooks/usePlanLimits';
import TokenProgressBar from './TokenProgressBar';
import Link from 'next/link';

interface TokenStatsModalProps {
  isOpen: boolean;
  onClose: () => void;
  shouldRefreshOnOpen?: boolean;
}

export default function TokenStatsModal({ isOpen, onClose, shouldRefreshOnOpen = false }: TokenStatsModalProps) {
  const planLimits = usePlanLimits();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FiBarChart className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Estadísticas de Uso de IA</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FiX className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {planLimits.loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Verificando plan...</span>
            </div>
          ) : planLimits.userPlan === 'free' ? (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Estadísticas de Tokens no disponibles
              </h3>
              <p className="text-gray-600 mb-6">
                Las estadísticas detalladas están disponibles para planes de pago.
              </p>
              <div>
                <Link
                  href="/"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Ver Planes Disponibles
                </Link>
              </div>
            </div>
          ) : planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 ? (
            <div className="mb-6">
              <TokenProgressBar
                used={planLimits.tokenUsage.current || 0}
                limit={planLimits.tokenUsage.limit || 0}
                percentage={planLimits.tokenUsage.percentage || 0}
                remaining={planLimits.tokenUsage.remaining || 0}
              />
            </div>
          ) : (
            <div className="flex justify-center items-center py-8">
              <p className="text-gray-600">No hay datos de uso disponibles.</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
}
