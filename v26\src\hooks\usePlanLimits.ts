// src/hooks/usePlanLimits.ts
// Hook React para validación de límites de plan

'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/supabaseClient';
import { PermissionService, PermissionCheck } from '@/lib/services/permissionService';
import { LimitHandler, LimitStatus } from '@/lib/services/limitHandler';

export interface PlanLimitsState {
  loading: boolean;
  userPlan: string | null;
  tokenUsage: {
    current: number;
    limit: number;
    percentage: number;
    remaining: number;
  } | null;
  limits: LimitStatus[];
  paymentVerified: boolean;
  error: string | null;
}

export interface FeatureAccessResult {
  allowed: boolean;
  loading: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  suggestedPlan?: string;
  tokenInfo?: any;
}

/**
 * Hook principal para gestión de límites de plan
 */
export function usePlanLimits() {
  const [state, setState] = useState<PlanLimitsState>({
    loading: true,
    userPlan: null,
    tokenUsage: null,
    limits: [],
    paymentVerified: false,
    error: null
  });

  const loadLimits = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const supabase = createClient();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Usuario no autenticado'
        }));
        return;
      }

      // Obtener perfil del usuario
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (profileError || !profile) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Perfil no encontrado'
        }));
        return;
      }

      // Calcular uso de tokens con validaciones defensivas
      const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
      const currentTokens = profile.current_month === currentMonth
        ? (profile.current_month_tokens || 0)  // Validar null/undefined
        : 0;

      const monthlyLimit = profile.monthly_token_limit || 0; // Validar null/undefined

      const tokenUsage = {
        current: currentTokens,
        limit: monthlyLimit,
        percentage: monthlyLimit > 0
          ? Math.round((currentTokens / monthlyLimit) * 100)
          : 0, // Evitar división por 0
        remaining: Math.max(0, monthlyLimit - currentTokens) // Evitar negativos
      };

      // Verificar límites usando versión cliente
      const limits = await LimitHandler.checkClientUserLimits();

      setState({
        loading: false,
        userPlan: profile.subscription_plan,
        tokenUsage,
        limits,
        paymentVerified: profile.payment_verified || profile.subscription_plan === 'free',
        error: null
      });

    } catch (error) {
      console.error('Error loading plan limits:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Error cargando límites'
      }));
    }
  }, []);

  useEffect(() => {
    loadLimits();
  }, [loadLimits]);

  const refresh = useCallback(() => {
    loadLimits();
  }, [loadLimits]);

  return {
    ...state,
    refresh
  };
}

/**
 * Hook para verificar acceso a una característica específica
 */
export function useFeatureAccess(
  feature: string,
  tokensRequired: number = 0,
  autoCheck: boolean = true
): FeatureAccessResult & { checkAccess: () => Promise<void> } {
  const [result, setResult] = useState<FeatureAccessResult>({
    allowed: false,
    loading: true
  });

  const checkAccess = useCallback(async () => {
    try {
      setResult(prev => ({ ...prev, loading: true }));

      const permission: PermissionCheck = PermissionService.createFeaturePermission(feature, tokensRequired);
      const permissionResult = await PermissionService.checkClientPermission(permission);

      setResult({
        allowed: permissionResult.granted,
        loading: false,
        reason: permissionResult.reason,
        upgradeRequired: permissionResult.upgradeRequired,
        suggestedPlan: permissionResult.suggestedPlan,
        tokenInfo: permissionResult.tokenInfo
      });

    } catch (error) {
      console.error('Error checking feature access:', error);
      setResult({
        allowed: false,
        loading: false,
        reason: 'Error verificando acceso'
      });
    }
  }, [feature, tokensRequired]);

  useEffect(() => {
    if (autoCheck) {
      checkAccess();
    }
  }, [autoCheck, checkAccess]);

  return {
    ...result,
    checkAccess
  };
}

/**
 * Hook para verificar si una acción está bloqueada
 */
export function useActionBlock(
  action: string,
  tokensRequired: number = 0
) {
  const [blocked, setBlocked] = useState<{
    isBlocked: boolean;
    loading: boolean;
    reason?: string;
    limitStatus?: LimitStatus;
  }>({
    isBlocked: false,
    loading: true
  });

  const checkBlock = useCallback(async () => {
    try {
      setBlocked(prev => ({ ...prev, loading: true }));

      const supabase = createClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        setBlocked({
          isBlocked: true,
          loading: false,
          reason: 'Usuario no autenticado'
        });
        return;
      }

      const blockResult = await LimitHandler.isClientActionBlocked(action, tokensRequired);

      setBlocked({
        isBlocked: blockResult.blocked,
        loading: false,
        reason: blockResult.reason,
        limitStatus: blockResult.limitStatus
      });

    } catch (error) {
      console.error('Error checking action block:', error);
      setBlocked({
        isBlocked: true,
        loading: false,
        reason: 'Error verificando bloqueo'
      });
    }
  }, [action, tokensRequired]);

  useEffect(() => {
    checkBlock();
  }, [checkBlock]);

  return {
    ...blocked,
    refresh: checkBlock
  };
}

/**
 * Hook para registrar uso después de una acción
 */
export function useUsageRecorder() {
  const recordUsage = useCallback(async (action: string, tokensUsed: number) => {
    try {
      // Usar la versión cliente que no requiere supabaseAdmin
      await LimitHandler.recordClientUsage(action, tokensUsed);

    } catch (error) {
      console.error('Error recording usage:', error);
    }
  }, []);

  return { recordUsage };
}

/**
 * Hook para obtener información de upgrade
 */
export function useUpgradeInfo() {
  const { userPlan, limits } = usePlanLimits();

  const getUpgradeRecommendation = useCallback(() => {
    if (!userPlan) return null;

    // Buscar límites que requieren acción
    const criticalLimit = limits.find(l => l.severity === 'exceeded' || l.severity === 'limit_reached');

    if (criticalLimit && criticalLimit.upgradeOptions && criticalLimit.upgradeOptions.length > 0) {
      return {
        reason: criticalLimit.message,
        suggestedPlan: criticalLimit.upgradeOptions[0].plan,
        benefits: criticalLimit.upgradeOptions[0].benefits,
        newLimit: criticalLimit.upgradeOptions[0].newLimit,
        urgency: criticalLimit.severity
      };
    }

    return null;
  }, [userPlan, limits]);

  const shouldShowUpgradePrompt = useCallback(() => {
    return limits.some(l => l.actionRequired);
  }, [limits]);

  return {
    getUpgradeRecommendation,
    shouldShowUpgradePrompt,
    upgradeUrl: '/upgrade-plan'
  };
}

/**
 * Hook combinado para validación completa de características
 */
export function useFeatureValidation(
  feature: string,
  tokensRequired: number = 0
) {
  const planLimits = usePlanLimits();
  const featureAccess = useFeatureAccess(feature, tokensRequired);
  const actionBlock = useActionBlock(feature, tokensRequired);
  const { recordUsage } = useUsageRecorder();
  const upgradeInfo = useUpgradeInfo();

  const canUseFeature = !actionBlock.isBlocked && featureAccess.allowed;
  const loading = planLimits.loading || featureAccess.loading || actionBlock.loading;

  const executeWithValidation = useCallback(async (
    action: () => Promise<any>,
    actualTokensUsed?: number
  ) => {
    if (!canUseFeature) {
      throw new Error(actionBlock.reason || featureAccess.reason || 'Acceso denegado');
    }

    try {
      const result = await action();
      
      // Registrar uso si se especifican tokens
      if (actualTokensUsed && actualTokensUsed > 0) {
        await recordUsage(feature, actualTokensUsed);
        planLimits.refresh(); // Refrescar límites después del uso
      }

      return result;
    } catch (error) {
      console.error('Error executing validated action:', error);
      throw error;
    }
  }, [canUseFeature, actionBlock.reason, featureAccess.reason, recordUsage, feature, planLimits]);

  return {
    canUseFeature,
    loading,
    reason: actionBlock.reason || featureAccess.reason,
    planLimits,
    featureAccess,
    actionBlock,
    upgradeInfo,
    executeWithValidation,
    refresh: () => {
      planLimits.refresh();
      featureAccess.checkAccess();
      actionBlock.refresh();
    }
  };
}
