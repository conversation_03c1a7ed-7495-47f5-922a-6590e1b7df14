// src/app/profile/page.tsx
// Página de perfil y configuración de cuenta del usuario

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  FiUser, 
  FiMail, 
  FiCalendar, 
  FiCreditCard, 
  FiBell, 
  FiSettings,
  FiArrowLeft,
  FiEdit2,
  FiSave,
  FiX
} from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';
import { createClient } from '@/lib/supabase/supabaseClient';
import AccountInfo from '@/features/profile/components/AccountInfo';
import NotificationHistory from '@/features/profile/components/NotificationHistory';
import PlanUsage from '@/features/profile/components/PlanUsage';
import AccountSettings from '@/features/profile/components/AccountSettings';

interface UserProfile {
  user: {
    id: string;
    email: string;
    name: string;
    created_at: string;
  };
  profile: {
    subscription_plan: string;
    plan_expires_at?: string;
    auto_renew: boolean;
    payment_verified: boolean;
    last_payment_date?: string;
  };
  access: {
    plan: string;
    features: string[];
    limits: any;
    currentUsage: any;
    paymentVerified: boolean;
  };
  tokenUsage: {
    current: number;
    limit: number;
    percentage: number;
    remaining: number;
  };
}

type TabType = 'account' | 'notifications' | 'usage' | 'settings';

export default function ProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('account');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    loadUserProfile();
  }, [user, router]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/user/profile');
      
      if (!response.ok) {
        throw new Error('Error cargando perfil de usuario');
      }

      const data = await response.json();
      setUserProfile(data);

    } catch (error) {
      console.error('Error loading user profile:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'account' as TabType, label: 'Información de Cuenta', icon: FiUser },
    { id: 'notifications' as TabType, label: 'Notificaciones', icon: FiBell },
    { id: 'usage' as TabType, label: 'Uso y Límites', icon: FiCreditCard },
    { id: 'settings' as TabType, label: 'Configuración', icon: FiSettings },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !userProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error cargando perfil</h2>
          <p className="text-gray-600 mb-4">{error || 'No se pudo cargar la información del perfil'}</p>
          <button
            onClick={() => router.push('/app')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Volver al Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/app')}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FiArrowLeft className="w-5 h-5 mr-2" />
                Volver al Dashboard
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-900">Mi Perfil</h1>
            <div className="w-24"></div> {/* Spacer for centering */}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-12 lg:gap-x-8">
          {/* Sidebar */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              {/* User Info */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FiUser className="w-10 h-10 text-blue-600" />
                </div>
                <h2 className="text-lg font-semibold text-gray-900">{userProfile.user.name}</h2>
                <p className="text-sm text-gray-600">{userProfile.user.email}</p>
                <div className="mt-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    userProfile.profile.subscription_plan === 'free' 
                      ? 'bg-gray-100 text-gray-800'
                      : userProfile.profile.subscription_plan === 'usuario'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-purple-100 text-purple-800'
                  }`}>
                    Plan {userProfile.profile.subscription_plan === 'free' ? 'Gratuito' : 
                          userProfile.profile.subscription_plan === 'usuario' ? 'Usuario' : 'Pro'}
                  </span>
                </div>
              </div>

              {/* Navigation */}
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="w-4 h-4 mr-3" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-9">
            <div className="bg-white rounded-lg shadow-sm">
              {activeTab === 'account' && (
                <AccountInfo 
                  userProfile={userProfile} 
                  onUpdate={loadUserProfile}
                />
              )}
              
              {activeTab === 'notifications' && (
                <NotificationHistory userId={userProfile.user.id} />
              )}
              
              {activeTab === 'usage' && (
                <PlanUsage userProfile={userProfile} />
              )}
              
              {activeTab === 'settings' && (
                <AccountSettings 
                  userProfile={userProfile}
                  onUpdate={loadUserProfile}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
