// src/app/api/admin/process-expired-grace-periods/route.ts
// Endpoint para procesar usuarios con período de gracia expirado

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { UserManagementService } from '@/lib/services/userManagement';
import { supabaseAdmin } from '@/lib/supabase/admin';

// Lista de emails de administradores autorizados
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Agregar más emails de administradores aquí
];

// Clave secreta para cron jobs (opcional, para mayor seguridad)
const CRON_SECRET = process.env.CRON_SECRET;

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Iniciando procesamiento de períodos de gracia expirados');

    // Verificar autenticación por cron secret (si está configurado)
    const authHeader = request.headers.get('authorization');
    if (CRON_SECRET && authHeader !== `Bearer ${CRON_SECRET}`) {
      // Si no hay cron secret, verificar autenticación de usuario admin
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return request.cookies.getAll();
            },
            setAll() {},
          },
        }
      );

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
        console.log(`❌ Acceso denegado para usuario: ${user?.email || 'anónimo'}`);
        return NextResponse.json({
          error: 'Acceso denegado. Solo administradores pueden ejecutar esta acción.'
        }, { status: 403 });
      }

      console.log(`👤 Administrador autorizado: ${user.email}`);
    } else if (CRON_SECRET) {
      console.log('🤖 Ejecutado por cron job autorizado');
    }

    // Ejecutar procesamiento de períodos de gracia expirados
    const startTime = Date.now();
    const result = await UserManagementService.processExpiredGracePeriods();
    const duration = Date.now() - startTime;

    console.log(`✅ Procesamiento completado en ${duration}ms`);

    return NextResponse.json({
      success: true,
      message: 'Procesamiento de períodos de gracia completado',
      result: {
        processed: result.processed,
        errors: result.errors,
        duration: `${duration}ms`
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error en procesamiento de períodos de gracia:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Endpoint GET para obtener estadísticas de usuarios en período de gracia
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Consultando estadísticas de períodos de gracia');

    // Verificar autenticación de administrador
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll() {},
        },
      }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || !user.email || !ADMIN_EMAILS.includes(user.email)) {
      return NextResponse.json({
        error: 'Acceso denegado'
      }, { status: 403 });
    }

    // Obtener estadísticas de usuarios en período de gracia
    const now = new Date().toISOString();

    // Usuarios actualmente en período de gracia
    const { data: usersInGrace, error: graceError } = await supabaseAdmin
      .from('user_profiles')
      .select('user_id, subscription_plan, plan_expires_at, security_flags')
      .eq('auto_renew', false)
      .neq('subscription_plan', 'free')
      .gt('plan_expires_at', now);

    // Usuarios con período de gracia expirado (pendientes de procesamiento)
    const { data: expiredGrace, error: expiredError } = await supabaseAdmin
      .from('user_profiles')
      .select('user_id, subscription_plan, plan_expires_at, security_flags')
      .eq('auto_renew', false)
      .neq('subscription_plan', 'free')
      .lt('plan_expires_at', now);

    if (graceError || expiredError) {
      throw new Error('Error obteniendo estadísticas');
    }

    // Filtrar solo usuarios realmente en período de gracia
    const activeGrace = usersInGrace?.filter(user => 
      user.security_flags?.subscription_cancelled === true
    ) || [];

    const pendingExpired = expiredGrace?.filter(user => 
      user.security_flags?.subscription_cancelled === true
    ) || [];

    // Calcular estadísticas por plan
    const graceByPlan = activeGrace.reduce((acc, user) => {
      acc[user.subscription_plan] = (acc[user.subscription_plan] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const expiredByPlan = pendingExpired.reduce((acc, user) => {
      acc[user.subscription_plan] = (acc[user.subscription_plan] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      statistics: {
        activeGracePeriods: {
          total: activeGrace.length,
          byPlan: graceByPlan,
          users: activeGrace.map(user => ({
            userId: user.user_id,
            plan: user.subscription_plan,
            expiresAt: user.plan_expires_at,
            hoursRemaining: Math.ceil(
              (new Date(user.plan_expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60)
            )
          }))
        },
        expiredPendingProcessing: {
          total: pendingExpired.length,
          byPlan: expiredByPlan,
          users: pendingExpired.map(user => ({
            userId: user.user_id,
            plan: user.subscription_plan,
            expiredAt: user.plan_expires_at,
            hoursOverdue: Math.ceil(
              (new Date().getTime() - new Date(user.plan_expires_at).getTime()) / (1000 * 60 * 60)
            )
          }))
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error obteniendo estadísticas de períodos de gracia:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
